<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>C标准分析工具启动页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 100px;
        }
        button {
            padding: 15px 30px;
            font-size: 18px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>C标准分析工具</h1>
    <p>点击下方按钮启动本地 GUI 工具：</p>
    <button onclick="launchTool()">启动工具</button>

    <script>
        function launchTool() {
            fetch('/launch-tool', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('工具已启动！');
                } else {
                    alert('启动失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误: ' + error);
            });
        }
    </script>
</body>
</html>