from flask import Flask, request, jsonify, send_from_directory
import subprocess
import os

app = Flask(__name__)

@app.route('/run-script', methods=['POST'])
def run_script():
    # 调用原脚本（不修改其内容）
    result = subprocess.run(['python', 'your_script.py'], capture_output=True, text=True)
    return jsonify({
        'stdout': result.stdout,
        'stderr': result.stderr
    })

@app.route('/launch-tool', methods=['POST'])
def launch_tool():
    # 导入并调用启动脚本
    import launch_script
    return jsonify(launch_script.launch_tool())

@app.route('/')
def index():
    # 提供一个简单的 HTML 页面
    return send_from_directory(os.getcwd(), 'index.html')

if __name__ == '__main__':
    app.run(debug=True)