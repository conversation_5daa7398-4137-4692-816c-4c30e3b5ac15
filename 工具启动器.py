#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具启动器 - 直接启动分析工具
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import os
import sys

class ToolLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("分析工具启动器")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面
        self.create_widgets()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = ttk.Label(title_frame, text="分析工具启动器", 
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="点击按钮启动对应的分析工具", 
                                  font=('Microsoft YaHei', 10))
        subtitle_label.pack(pady=(5, 0))
        
        # 分隔线
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill=tk.X, padx=20, pady=10)
        
        # 工具按钮区域
        tools_frame = ttk.Frame(self.root)
        tools_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # C标准化分析工具
        c_frame = ttk.LabelFrame(tools_frame, text="C标准化分析工具", padding=15)
        c_frame.pack(fill=tk.X, pady=(0, 10))
        
        c_desc = ttk.Label(c_frame, text="分析C程序的CPU、内存和性能数据\n支持批量分析和图表展示", 
                          font=('Microsoft YaHei', 9))
        c_desc.pack(anchor=tk.W, pady=(0, 10))
        
        c_btn = ttk.Button(c_frame, text="启动 C标准化分析工具", 
                          command=self.launch_c_tool, style='Accent.TButton')
        c_btn.pack(anchor=tk.W)
        
        # Java标准化分析工具
        java_frame = ttk.LabelFrame(tools_frame, text="Java标准化分析工具", padding=15)
        java_frame.pack(fill=tk.X, pady=(0, 10))
        
        java_desc = ttk.Label(java_frame, text="分析Java程序的CPU、内存、性能和模型加密数据\n支持多种数据格式和批量处理", 
                             font=('Microsoft YaHei', 9))
        java_desc.pack(anchor=tk.W, pady=(0, 10))
        
        java_btn = ttk.Button(java_frame, text="启动 Java标准化分析工具", 
                             command=self.launch_java_tool, style='Accent.TButton')
        java_btn.pack(anchor=tk.W)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        separator2 = ttk.Separator(status_frame, orient='horizontal')
        separator2.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="就绪", 
                                     font=('Microsoft YaHei', 9))
        self.status_label.pack(anchor=tk.W)
        
        # 退出按钮
        exit_btn = ttk.Button(status_frame, text="退出", command=self.root.quit)
        exit_btn.pack(anchor=tk.E, pady=(10, 0))
    
    def launch_c_tool(self):
        """启动C标准化分析工具"""
        script_path = "C标准分析工具UI.py"
        self.launch_tool(script_path, "C标准化分析工具")
    
    def launch_java_tool(self):
        """启动Java标准化分析工具"""
        script_path = "Java标准化分析工具UI.py"
        self.launch_tool(script_path, "Java标准化分析工具")
    
    def launch_tool(self, script_path, tool_name):
        """启动工具"""
        try:
            # 检查脚本文件是否存在
            if not os.path.exists(script_path):
                messagebox.showerror("错误", f"脚本文件不存在: {script_path}")
                return
            
            self.status_label.config(text=f"正在启动 {tool_name}...")
            self.root.update()
            
            # 启动脚本
            if os.name == 'nt':  # Windows
                subprocess.Popen(['python', script_path], 
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:  # Linux/Mac
                subprocess.Popen(['python', script_path])
            
            self.status_label.config(text=f"{tool_name} 已启动")
            messagebox.showinfo("成功", f"{tool_name} 已成功启动！")
            
        except Exception as e:
            error_msg = f"启动 {tool_name} 失败: {str(e)}"
            self.status_label.config(text="启动失败")
            messagebox.showerror("错误", error_msg)

def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置主题样式
    style = ttk.Style()
    if 'winnative' in style.theme_names():
        style.theme_use('winnative')
    elif 'clam' in style.theme_names():
        style.theme_use('clam')
    
    # 创建应用
    app = ToolLauncher(root)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()
