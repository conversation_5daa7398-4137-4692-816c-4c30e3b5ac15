#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import config
        print("✓ config模块导入成功")
    except Exception as e:
        print(f"✗ config模块导入失败: {e}")
        return False
    
    try:
        import auth
        print("✓ auth模块导入成功")
    except Exception as e:
        print(f"✗ auth模块导入失败: {e}")
        return False
    
    try:
        from tools import ToolManager
        print("✓ tools模块导入成功")
    except Exception as e:
        print(f"✗ tools模块导入失败: {e}")
        return False
    
    try:
        import app
        print("✓ app模块导入成功")
    except Exception as e:
        print(f"✗ app模块导入失败: {e}")
        return False
    
    return True

def test_tool_manager():
    """测试工具管理器"""
    print("\n测试工具管理器...")
    
    try:
        from tools import ToolManager
        manager = ToolManager()
        
        tools = manager.get_all_tools()
        print(f"✓ 发现 {len(tools)} 个工具")
        
        for tool in tools:
            info = tool.get_info()
            print(f"  - {info['name']} ({info['id']})")
        
        return True
    except Exception as e:
        print(f"✗ 工具管理器测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from config import Config
        
        print(f"✓ 上传目录: {Config.UPLOAD_FOLDER}")
        print(f"✓ 结果目录: {Config.RESULTS_FOLDER}")
        print(f"✓ 用户数量: {len(Config.USERS)}")
        print(f"✓ 工具配置数量: {len(Config.TOOLS_CONFIG)}")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_directories():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = [
        'templates',
        'static',
        'static/uploads',
        'static/results',
        'static/css',
        'static/js',
        'tools'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✓ {dir_path}")
        else:
            print(f"✗ {dir_path} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 50)
    print("分析工具平台 - 应用测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置", test_config),
        ("目录结构", test_directories),
        ("工具管理器", test_tool_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用可以正常启动。")
        print("\n启动命令:")
        print("  python run.py")
        print("  或者双击 start.bat")
        print("\n访问地址: http://localhost:5000")
        print("登录信息: lcj / 123")
    else:
        print("❌ 部分测试失败，请检查配置。")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
