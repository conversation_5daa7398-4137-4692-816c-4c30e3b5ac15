#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os
from datetime import timedelta

class Config:
    """应用配置"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
    RESULTS_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'results')
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'txt', 'json', 'log', 'csv', 'dat'}
    
    # Session配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # 用户认证配置
    USERS = {
        'lcj': '123'  # 用户名: 密码
    }
    
    # 工具配置
    TOOLS_CONFIG = {
        'c_analysis': {
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'script_path': '../C标准分析工具UI.py',
            'icon': 'fas fa-code',
            'color': 'primary',
            'file_types': [
                {
                    'key': 'cpu_file',
                    'label': 'CPU数据文件',
                    'accept': '.txt',
                    'required': False
                },
                {
                    'key': 'memory_file', 
                    'label': '内存数据文件',
                    'accept': '.txt',
                    'required': False
                },
                {
                    'key': 'performance_file',
                    'label': '性能数据文件', 
                    'accept': '.txt',
                    'required': False
                },
                {
                    'key': 'batch_dir',
                    'label': '批量分析目录',
                    'type': 'directory',
                    'required': False
                }
            ]
        },
        'java_analysis': {
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'script_path': '../Java标准化分析工具UI.py',
            'icon': 'fab fa-java',
            'color': 'warning',
            'file_types': [
                {
                    'key': 'cpu_file',
                    'label': 'CPU数据文件',
                    'accept': '.txt,.json',
                    'required': False
                },
                {
                    'key': 'memory_file',
                    'label': '内存数据文件',
                    'accept': '.txt,.json', 
                    'required': False
                },
                {
                    'key': 'performance_file',
                    'label': '性能数据文件',
                    'accept': '.txt,.json',
                    'required': False
                },
                {
                    'key': 'encryption_file',
                    'label': '模型加密日志文件',
                    'accept': '.txt,.log',
                    'required': False
                },
                {
                    'key': 'batch_path',
                    'label': '批量分析路径',
                    'type': 'text',
                    'required': False
                }
            ]
        }
    }
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        pass
