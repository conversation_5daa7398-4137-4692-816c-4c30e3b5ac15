# 网页版分析工具功能说明

## 🌐 功能概述

新增了网页版分析工具功能，用户现在可以选择三种方式使用分析工具：

1. **网页版启动**（推荐）- 直接在浏览器中进行分析
2. **客户端启动** - 下载EXE程序到本地运行
3. **服务器端启动** - 在服务器电脑上运行

## ✨ 网页版特性

### 支持的工具
- ✅ **C标准化分析工具** - 完整的网页版实现
- ✅ **Java标准化分析工具** - 完整的网页版实现
- ❌ **商用车标准化测试统计工具** - 暂不支持网页版

### 核心功能
- **文件上传**: 支持拖拽上传，多文件同时上传
- **实时分析**: 在线分析数据，无需下载安装
- **图表展示**: 使用Chart.js生成交互式图表
- **结果导出**: 支持分析结果下载
- **跨平台**: 支持所有现代浏览器

## 🔧 技术实现

### C标准化分析工具网页版

#### 支持的文件类型
- `stability_CPU_*.txt` - CPU使用率数据
- `stability_Memory_*.txt` - 内存使用数据  
- `stability_Detect_*.txt` - 性能检测数据

#### 分析功能
1. **CPU分析**
   - 平均/最大/最小使用率统计
   - CPU使用率趋势图表
   - 数据点统计

2. **内存分析**
   - 平均/最大/最小使用量统计
   - 内存使用量趋势图表
   - 内存占用分析

3. **性能分析**
   - 检测时间统计
   - 性能指标分析
   - 检测次数统计

#### 数据格式
```
# CPU数据格式
时间戳 CPU使用率
2024-01-01_10:00:00 45.2
2024-01-01_10:00:01 47.8

# 内存数据格式  
时间戳 内存使用量(MB)
2024-01-01_10:00:00 1024.5
2024-01-01_10:00:01 1056.2

# 性能数据格式
时间戳 检测时间(ms)
2024-01-01_10:00:00 15.6
2024-01-01_10:00:01 18.2
```

### Java标准化分析工具网页版

#### 支持的文件类型
- CPU/内存数据文件
- 性能数据文件
- 模型加密日志文件

#### 分析功能
1. **CPU/内存统计分析**
   - CPU和内存使用率双轴图表
   - 平均使用率统计
   - 峰值分析

2. **性能模块分析**
   - 各模块执行时间统计
   - 模块性能对比图表
   - 执行次数统计

3. **加密检测分析**
   - 加密算法检测统计
   - 检测成功率分析
   - 算法性能对比

4. **报告生成**
   - 综合分析报告
   - 性能建议
   - 数据汇总

#### 数据格式
```
# CPU/内存数据格式
时间戳 CPU使用率 内存使用量(MB)
2024-01-01_10:00:00 45.2 1024.5
2024-01-01_10:00:01 47.8 1056.2

# 性能数据格式
模块名 执行时间(ms) 状态
ModuleA 125.6 success
ModuleB 89.3 success

# 加密数据格式
时间戳 加密算法 检测结果
2024-01-01_10:00:00 AES success
2024-01-01_10:00:01 RSA failed
```

## 🖥️ 用户界面

### 工具选择页面
- 三种启动方式的对比展示
- 网页版作为推荐选项
- 详细的功能说明

### 网页版分析界面
1. **文件上传区域**
   - 拖拽上传支持
   - 多文件选择
   - 上传进度显示
   - 文件列表管理

2. **分析设置区域**
   - 分析选项配置
   - 功能模块选择
   - 一键开始分析

3. **结果展示区域**
   - 统计概览卡片
   - 交互式图表
   - 详细分析结果
   - 下载链接

## 📊 功能对比

| 特性 | 网页版 | 客户端版 | 服务器版 |
|------|--------|----------|----------|
| 安装需求 | 无需安装 | 需下载EXE | 无需安装 |
| 运行位置 | 浏览器 | 用户电脑 | 服务器电脑 |
| 跨平台 | ✅ 全平台 | ❌ Windows | ❌ 服务器系统 |
| 实时性 | ✅ 实时 | ✅ 实时 | ✅ 实时 |
| 离线使用 | ❌ 需网络 | ✅ 支持 | ❌ 需网络 |
| 资源占用 | 服务器 | 用户电脑 | 服务器电脑 |
| 更新维护 | 自动 | 需重新下载 | 自动 |

## 🚀 使用流程

### 网页版使用流程
1. 登录Web平台
2. 选择C或Java分析工具
3. 点击"网页版启动（推荐）"
4. 上传数据文件（拖拽或点击选择）
5. 配置分析选项
6. 点击"开始分析"
7. 查看分析结果和图表
8. 下载分析报告

### 文件上传
- 支持拖拽文件到上传区域
- 支持点击选择多个文件
- 实时显示上传进度
- 可以随时删除已上传文件

### 分析配置
- **C工具**: CPU分析、内存分析、性能分析
- **Java工具**: CPU/内存统计、性能模块、加密检测、报告生成

## 🔒 安全特性

### 数据安全
- 用户文件存储在独立目录
- 基于用户会话的文件隔离
- 自动清理临时文件
- 安全的文件名处理

### 访问控制
- 需要登录才能使用
- 用户会话验证
- 文件访问权限控制
- 防止路径遍历攻击

## ⚠️ 注意事项

### 文件要求
- 最大文件大小: 100MB
- 支持格式: .txt, .json, .log
- 编码要求: UTF-8
- 数据格式需符合规范

### 浏览器要求
- 现代浏览器（Chrome, Firefox, Safari, Edge）
- 支持JavaScript和HTML5
- 建议使用最新版本浏览器

### 性能建议
- 大文件分析可能需要较长时间
- 建议在网络稳定环境下使用
- 避免同时上传过多大文件

## 🎯 优势总结

### 用户体验
- **即开即用**: 无需下载安装，打开浏览器即可使用
- **跨平台**: 支持Windows、Mac、Linux等所有平台
- **实时分析**: 在线处理，结果即时显示
- **交互式图表**: 现代化的数据可视化

### 技术优势
- **功能完整**: 与桌面版功能完全一致
- **自动更新**: 功能更新无需用户操作
- **集中管理**: 统一的用户管理和数据处理
- **扩展性好**: 易于添加新功能和工具

### 管理优势
- **统一平台**: 所有工具集中在一个平台
- **用户管理**: 支持多用户并发使用
- **数据安全**: 集中的数据管理和备份
- **维护简单**: 服务器端统一维护

现在用户可以根据需要选择最适合的使用方式，网页版提供了最便捷的分析体验！🎉
