# Java标准化分析工具检测测试数据 - JSON格式
# 前5行为注释，从第6行开始处理
# 这是注释行1
# 这是注释行2
# 这是注释行3
# 这是注释行4
{"module": "ModuleA", "costTime": 125.6, "time": "2024-01-01_10:00:00"}
{"module": "ModuleB", "costTime": 89.3, "time": "2024-01-01_10:00:01"}
{"module": "ModuleC", "costTime": 156.8, "time": "2024-01-01_10:00:02"}
{"module": "ModuleA", "costTime": 132.1, "time": "2024-01-01_10:00:03"}
{"module": "ModuleB", "costTime": 91.7, "time": "2024-01-01_10:00:04"}
{"module": "ModuleC", "costTime": 149.2, "time": "2024-01-01_10:00:05"}
{"module": "ModuleA", "costTime": 128.4, "time": "2024-01-01_10:00:06"}
{"module": "ModuleB", "costTime": 87.9, "time": "2024-01-01_10:00:07"}
{"module": "ModuleC", "costTime": 152.3, "time": "2024-01-01_10:00:08"}
{"module": "ModuleA", "costTime": 130.7, "time": "2024-01-01_10:00:09"}
{"module": "ModuleB", "costTime": 93.2, "time": "2024-01-01_10:00:10"}
{"module": "ModuleC", "costTime": 147.6, "time": "2024-01-01_10:00:11"}
{"module": "ModuleA", "costTime": 126.9, "time": "2024-01-01_10:00:12"}
{"module": "ModuleB", "costTime": 88.5, "time": "2024-01-01_10:00:13"}
{"module": "ModuleC", "costTime": 154.1, "time": "2024-01-01_10:00:14"}
{"module": "ModuleA", "costTime": 129.3, "time": "2024-01-01_10:00:15"}
