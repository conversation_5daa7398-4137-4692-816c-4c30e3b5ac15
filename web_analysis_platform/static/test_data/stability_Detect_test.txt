# C标准分析工具性能测试数据 - JSON格式
# 前5行为注释，从第6行开始处理
# 这是注释行1
# 这是注释行2
# 这是注释行3
# 这是注释行4
{"module": "DetectionModule", "costTime": 15.6, "time": "2024-01-01_10:00:00", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 18.2, "time": "2024-01-01_10:00:01", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 16.8, "time": "2024-01-01_10:00:02", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 17.5, "time": "2024-01-01_10:00:03", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 19.1, "time": "2024-01-01_10:00:04", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 16.3, "time": "2024-01-01_10:00:05", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 18.7, "time": "2024-01-01_10:00:06", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 15.9, "time": "2024-01-01_10:00:07", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 17.2, "time": "2024-01-01_10:00:08", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 18.4, "time": "2024-01-01_10:00:09", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 16.7, "time": "2024-01-01_10:00:10", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 19.3, "time": "2024-01-01_10:00:11", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 15.4, "time": "2024-01-01_10:00:12", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 17.8, "time": "2024-01-01_10:00:13", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 18.9, "time": "2024-01-01_10:00:14", "g_stageStability": 1}
{"module": "DetectionModule", "costTime": 16.1, "time": "2024-01-01_10:00:15", "g_stageStability": 1}
