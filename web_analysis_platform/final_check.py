#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终检查脚本
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_all():
    """检查所有组件"""
    print("=" * 60)
    print("分析工具平台 - 最终检查")
    print("=" * 60)
    
    checks = []
    
    # 1. 检查文件结构
    print("\n1. 检查文件结构...")
    required_files = [
        'app.py', 'config.py', 'auth.py', 'run.py',
        'templates/dashboard.html', 'templates/login.html',
        'tools/__init__.py', 'tools/base_tool.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file}")
            missing_files.append(file)
    
    checks.append(("文件结构", len(missing_files) == 0))
    
    # 2. 检查脚本路径
    print("\n2. 检查脚本路径...")
    try:
        from config import Config
        
        for tool_id, tool_config in Config.TOOLS_CONFIG.items():
            script_path = tool_config['script_path']
            
            # 解析绝对路径
            if not os.path.isabs(script_path):
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                abs_path = os.path.abspath(os.path.join(base_dir, script_path))
            else:
                abs_path = script_path
            
            if os.path.exists(abs_path):
                print(f"  ✓ {tool_config['name']}: {abs_path}")
            else:
                print(f"  ✗ {tool_config['name']}: {abs_path} (不存在)")
        
        checks.append(("脚本路径", True))
        
    except Exception as e:
        print(f"  ✗ 检查脚本路径失败: {e}")
        checks.append(("脚本路径", False))
    
    # 3. 检查应用导入
    print("\n3. 检查应用导入...")
    try:
        import app
        print("  ✓ Flask应用导入成功")
        checks.append(("应用导入", True))
    except Exception as e:
        print(f"  ✗ Flask应用导入失败: {e}")
        checks.append(("应用导入", False))
    
    # 4. 检查工具管理器
    print("\n4. 检查工具管理器...")
    try:
        from tools import ToolManager
        manager = ToolManager()
        tools = manager.get_all_tools()
        print(f"  ✓ 工具管理器正常，发现 {len(tools)} 个工具")
        for tool in tools:
            info = tool.get_info()
            print(f"    - {info['name']} ({info['id']})")
        checks.append(("工具管理器", True))
    except Exception as e:
        print(f"  ✗ 工具管理器失败: {e}")
        checks.append(("工具管理器", False))
    
    # 总结
    print("\n" + "=" * 60)
    print("检查结果:")
    print("=" * 60)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in checks:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{check_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！")
        print("\n启动方式:")
        print("1. 双击 start.bat")
        print("2. 或运行: python run.py")
        print("\n访问地址: http://localhost:5000")
        print("登录信息: lcj / 123")
        print("\n功能说明:")
        print("- 点击工具卡片直接启动原始GUI工具")
        print("- 支持同时启动多个工具实例")
        print("- 保持原始工具的所有功能")
    else:
        print("\n❌ 部分检查失败，请检查配置")
    
    print("=" * 60)

if __name__ == '__main__':
    check_all()
