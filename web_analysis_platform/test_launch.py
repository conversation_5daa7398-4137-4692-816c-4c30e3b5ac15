#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工具启动功能
"""

import os
import sys
import subprocess

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config

def test_tool_launch(tool_id):
    """测试工具启动"""
    if tool_id not in Config.TOOLS_CONFIG:
        print(f"工具 {tool_id} 不存在")
        return False
    
    tool_config = Config.TOOLS_CONFIG[tool_id]
    script_path = tool_config['script_path']
    
    # 解析绝对路径
    if not os.path.isabs(script_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, script_path))
    else:
        abs_path = script_path
    
    print(f"测试启动工具: {tool_config['name']}")
    print(f"脚本路径: {abs_path}")
    
    if not os.path.exists(abs_path):
        print("✗ 脚本文件不存在")
        return False
    
    try:
        print("正在启动工具...")
        # 启动脚本（非阻塞方式）
        process = subprocess.Popen(['python', abs_path], 
                                 cwd=os.path.dirname(abs_path),
                                 creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        print(f"✓ 工具启动成功，进程ID: {process.pid}")
        print("注意：工具窗口应该已经打开")
        return True
        
    except Exception as e:
        print(f"✗ 启动失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("工具启动测试")
    print("=" * 50)
    
    print("可用工具:")
    for tool_id, config in Config.TOOLS_CONFIG.items():
        print(f"  {tool_id}: {config['name']}")
    
    print("\n选择要测试的工具:")
    print("1. c_analysis (C标准化分析工具)")
    print("2. java_analysis (Java标准化分析工具)")
    print("3. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_tool_launch('c_analysis')
            break
        elif choice == '2':
            test_tool_launch('java_analysis')
            break
        elif choice == '3':
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
