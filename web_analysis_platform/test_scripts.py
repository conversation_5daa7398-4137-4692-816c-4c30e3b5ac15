#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本路径
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config

def test_script_paths():
    """测试脚本路径是否存在"""
    print("测试脚本路径...")
    print("=" * 50)
    
    for tool_id, tool_config in Config.TOOLS_CONFIG.items():
        tool_name = tool_config['name']
        script_path = tool_config['script_path']
        
        print(f"\n工具: {tool_name}")
        print(f"ID: {tool_id}")
        print(f"配置路径: {script_path}")
        
        # 解析绝对路径
        if not os.path.isabs(script_path):
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            abs_path = os.path.abspath(os.path.join(base_dir, script_path))
        else:
            abs_path = script_path
            
        print(f"绝对路径: {abs_path}")
        
        if os.path.exists(abs_path):
            print("✓ 脚本文件存在")
        else:
            print("✗ 脚本文件不存在")
            
            # 尝试查找可能的位置
            possible_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', os.path.basename(script_path)),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), os.path.basename(script_path)),
                os.path.join(os.getcwd(), os.path.basename(script_path))
            ]
            
            print("  尝试查找脚本文件...")
            for path in possible_paths:
                abs_possible = os.path.abspath(path)
                if os.path.exists(abs_possible):
                    print(f"  ✓ 找到脚本: {abs_possible}")
                    break
            else:
                print("  ✗ 未找到脚本文件")
        
        print("-" * 30)

if __name__ == '__main__':
    test_script_paths()
