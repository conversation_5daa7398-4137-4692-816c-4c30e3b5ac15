#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全一致功能验证脚本
"""

import os
import sys
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_parsing():
    """测试数据解析功能"""
    print("=" * 60)
    print("数据解析功能测试")
    print("=" * 60)
    
    try:
        from app import parse_cpu_data, parse_memory_data, parse_performance_data
        from app import parse_java_cpu_memory_data, parse_java_performance_data
        
        # 测试C工具数据解析
        print("\n测试C工具数据解析:")
        
        # 测试CPU数据
        cpu_test_file = 'static/test_data/stability_CPU_test.txt'
        if os.path.exists(cpu_test_file):
            with open(cpu_test_file, 'r', encoding='utf-8') as f:
                cpu_content = f.read()
            cpu_data = parse_cpu_data(cpu_content)
            print(f"✓ CPU数据解析: 解析出 {len(cpu_data)} 条记录")
            if cpu_data:
                print(f"  示例数据: {cpu_data[0]}")
        else:
            print("✗ CPU测试文件不存在")
        
        # 测试内存数据
        memory_test_file = 'static/test_data/stability_Memory_test.txt'
        if os.path.exists(memory_test_file):
            with open(memory_test_file, 'r', encoding='utf-8') as f:
                memory_content = f.read()
            memory_data = parse_memory_data(memory_content)
            print(f"✓ 内存数据解析: 解析出 {len(memory_data)} 条记录")
            if memory_data:
                print(f"  示例数据: {memory_data[0]}")
        else:
            print("✗ 内存测试文件不存在")
        
        # 测试性能数据
        performance_test_file = 'static/test_data/stability_Detect_test.txt'
        if os.path.exists(performance_test_file):
            with open(performance_test_file, 'r', encoding='utf-8') as f:
                performance_content = f.read()
            performance_data = parse_performance_data(performance_content)
            print(f"✓ 性能数据解析: 解析出 {len(performance_data)} 条记录")
            if performance_data:
                print(f"  示例数据: {performance_data[0]}")
        else:
            print("✗ 性能测试文件不存在")
        
        # 测试Java工具数据解析
        print("\n测试Java工具数据解析:")
        
        # 测试Java CPU/内存数据
        java_resource_file = 'static/test_data/stability_resource_test.txt'
        if os.path.exists(java_resource_file):
            with open(java_resource_file, 'r', encoding='utf-8') as f:
                java_content = f.read()
            java_data = parse_java_cpu_memory_data(java_content)
            print(f"✓ Java CPU/内存数据解析: 解析出 {len(java_data)} 条记录")
            if java_data:
                print(f"  示例数据: {java_data[0]}")
        else:
            print("✗ Java资源测试文件不存在")
        
        # 测试Java性能数据
        java_detect_file = 'static/test_data/java_stability_detect_test.txt'
        if os.path.exists(java_detect_file):
            with open(java_detect_file, 'r', encoding='utf-8') as f:
                java_detect_content = f.read()
            java_perf_data = parse_java_performance_data(java_detect_content)
            print(f"✓ Java性能数据解析: 解析出 {len(java_perf_data)} 条记录")
            if java_perf_data:
                print(f"  示例数据: {java_perf_data[0]}")
        else:
            print("✗ Java检测测试文件不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据解析测试失败: {e}")
        return False

def test_analysis_functions():
    """测试分析功能"""
    print("\n" + "=" * 60)
    print("分析功能测试")
    print("=" * 60)
    
    try:
        from app import analyze_cpu_usage, analyze_memory_usage, analyze_performance_data
        from app import analyze_java_cpu_memory, analyze_java_performance
        from app import parse_cpu_data, parse_memory_data, parse_performance_data
        from app import parse_java_cpu_memory_data, parse_java_performance_data
        
        # 测试C工具分析
        print("\n测试C工具分析功能:")
        
        # 测试CPU分析
        cpu_test_file = 'static/test_data/stability_CPU_test.txt'
        if os.path.exists(cpu_test_file):
            with open(cpu_test_file, 'r', encoding='utf-8') as f:
                cpu_content = f.read()
            cpu_data = parse_cpu_data(cpu_content)
            if cpu_data:
                cpu_stats = analyze_cpu_usage(cpu_data)
                print(f"✓ CPU分析: {cpu_stats['stats']}")
            else:
                print("✗ CPU数据为空，无法分析")
        
        # 测试内存分析
        memory_test_file = 'static/test_data/stability_Memory_test.txt'
        if os.path.exists(memory_test_file):
            with open(memory_test_file, 'r', encoding='utf-8') as f:
                memory_content = f.read()
            memory_data = parse_memory_data(memory_content)
            if memory_data:
                memory_stats = analyze_memory_usage(memory_data)
                print(f"✓ 内存分析: {memory_stats['stats']}")
            else:
                print("✗ 内存数据为空，无法分析")
        
        # 测试性能分析
        performance_test_file = 'static/test_data/stability_Detect_test.txt'
        if os.path.exists(performance_test_file):
            with open(performance_test_file, 'r', encoding='utf-8') as f:
                performance_content = f.read()
            performance_data = parse_performance_data(performance_content)
            if performance_data:
                perf_stats = analyze_performance_data(performance_data)
                print(f"✓ 性能分析: {perf_stats['stats']}")
            else:
                print("✗ 性能数据为空，无法分析")
        
        # 测试Java工具分析
        print("\n测试Java工具分析功能:")
        
        # 测试Java CPU/内存分析
        java_resource_file = 'static/test_data/stability_resource_test.txt'
        if os.path.exists(java_resource_file):
            with open(java_resource_file, 'r', encoding='utf-8') as f:
                java_content = f.read()
            java_data = parse_java_cpu_memory_data(java_content)
            if java_data:
                java_stats = analyze_java_cpu_memory(java_data)
                print(f"✓ Java CPU/内存分析: {java_stats['stats']}")
            else:
                print("✗ Java CPU/内存数据为空，无法分析")
        
        # 测试Java性能分析
        java_detect_file = 'static/test_data/java_stability_detect_test.txt'
        if os.path.exists(java_detect_file):
            with open(java_detect_file, 'r', encoding='utf-8') as f:
                java_detect_content = f.read()
            java_perf_data = parse_java_performance_data(java_detect_content)
            if java_perf_data:
                java_perf_stats = analyze_java_performance(java_perf_data)
                print(f"✓ Java性能分析: {java_perf_stats['stats']}")
            else:
                print("✗ Java性能数据为空，无法分析")
        
        return True
        
    except Exception as e:
        print(f"✗ 分析功能测试失败: {e}")
        return False

def test_batch_analysis():
    """测试批量分析功能"""
    print("\n" + "=" * 60)
    print("批量分析功能测试")
    print("=" * 60)
    
    try:
        from app import auto_match_files
        
        # 模拟C工具批量文件
        c_batch_files = [
            {'filename': 'stability_CPU_20240101.txt', 'filepath': 'static/test_data/stability_CPU_test.txt'},
            {'filename': 'stability_Memory_20240101.txt', 'filepath': 'static/test_data/stability_Memory_test.txt'},
            {'filename': 'stability_Detect_20240101.txt', 'filepath': 'static/test_data/stability_Detect_test.txt'}
        ]
        
        c_matched = auto_match_files('c_analysis', c_batch_files)
        print("C工具文件匹配结果:")
        for key, value in c_matched.items():
            print(f"  {key}: {value['filename']}")
        
        # 模拟Java工具批量文件
        java_batch_files = [
            {'filename': 'stability_resource_20240101.txt', 'filepath': 'static/test_data/stability_resource_test.txt'},
            {'filename': 'java_stability_detect_20240101.txt', 'filepath': 'static/test_data/java_stability_detect_test.txt'}
        ]
        
        java_matched = auto_match_files('java_analysis', java_batch_files)
        print("\nJava工具文件匹配结果:")
        for key, value in java_matched.items():
            print(f"  {key}: {value['filename']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 批量分析测试失败: {e}")
        return False

def main():
    """主函数"""
    print("完全一致功能验证")
    print("检查数据格式、解析逻辑、分析功能是否与原始脚本一致")
    
    # 检查测试数据文件
    print("\n检查测试数据文件:")
    test_files = [
        'static/test_data/stability_CPU_test.txt',
        'static/test_data/stability_Memory_test.txt', 
        'static/test_data/stability_Detect_test.txt',
        'static/test_data/stability_resource_test.txt',
        'static/test_data/java_stability_detect_test.txt'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")
    
    # 执行测试
    test1 = test_data_parsing()
    test2 = test_analysis_functions()
    test3 = test_batch_analysis()
    
    print("\n" + "=" * 60)
    if test1 and test2 and test3:
        print("🎉 所有功能验证通过！")
        print("\n关键修复:")
        print("✓ 数据格式改为JSON格式（与原始脚本一致）")
        print("✓ 数据解析逻辑完全一致")
        print("✓ 批量分析自动文件匹配功能")
        print("✓ 分析结果计算方法一致")
        
        print("\n使用说明:")
        print("1. 启动应用: python app.py")
        print("2. 访问: http://localhost:5000")
        print("3. 选择C或Java工具，点击'网页版启动'")
        print("4. 测试单个分析: 选择标签页 → 浏览文件 → 分析")
        print("5. 测试批量分析: 选择目录 → 批量分析 → 自动填充文件路径")
        
    else:
        print("❌ 功能验证存在问题，请检查实现")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
