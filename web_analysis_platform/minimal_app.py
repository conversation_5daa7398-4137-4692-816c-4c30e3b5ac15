#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化应用 - 直接启动工具
"""

from flask import Flask, render_template_string, redirect, flash, session
import os
import subprocess

app = Flask(__name__)
app.secret_key = 'test-secret-key'

# HTML模板
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析工具平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        .tool-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>分析工具平台
            </a>
        </div>
    </nav>
    
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-tachometer-alt me-2"></i>分析工具仪表板
                        </h2>
                        <p class="card-text text-muted">
                            点击下方按钮直接启动对应的分析工具GUI
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="tool-icon text-primary">
                            <i class="fas fa-code"></i>
                        </div>
                        <h5 class="card-title">C标准化分析工具</h5>
                        <p class="card-text text-muted">点击直接启动C标准分析工具GUI</p>
                        <a href="/tool/c_analysis" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>启动GUI工具
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="tool-icon text-warning">
                            <i class="fab fa-java"></i>
                        </div>
                        <h5 class="card-title">Java标准化分析工具</h5>
                        <p class="card-text text-muted">点击直接启动Java标准化分析工具GUI</p>
                        <a href="/tool/java_analysis" class="btn btn-warning">
                            <i class="fas fa-external-link-alt me-2"></i>启动GUI工具
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

@app.route('/')
def dashboard():
    """仪表板"""
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/tool/<tool_id>')
def tool_interface(tool_id):
    """工具界面 - 直接启动原始脚本"""
    
    # 脚本路径映射
    script_paths = {
        'c_analysis': '../C标准分析工具UI.py',
        'java_analysis': '../Java标准化分析工具UI.py'
    }
    
    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }
    
    if tool_id not in script_paths:
        flash('工具不存在！', 'error')
        return redirect('/')
    
    script_path = script_paths[tool_id]
    
    # 解析绝对路径
    if not os.path.isabs(script_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, script_path))
    else:
        abs_path = script_path
    
    print(f"尝试启动工具: {tool_names[tool_id]}")
    print(f"脚本路径: {abs_path}")
    
    if not os.path.exists(abs_path):
        flash(f'脚本文件不存在: {abs_path}', 'error')
        print(f"错误: 脚本文件不存在")
        return redirect('/')
    
    try:
        # 启动脚本（非阻塞方式）
        process = subprocess.Popen(['python', abs_path], 
                        cwd=os.path.dirname(abs_path),
                        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        flash(f'{tool_names[tool_id]} 已启动！进程ID: {process.pid}', 'success')
        print(f"成功启动工具，进程ID: {process.pid}")
        return redirect('/')
        
    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        print(f"启动失败: {str(e)}")
        return redirect('/')

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print("功能: 点击工具卡片直接启动原始GUI工具")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
