# 商用车标准化测试统计工具模块

## 🚛 模块概述

新增了商用车标准化测试统计工具模块，为用户提供专业的商用车测试数据分析和统计功能。

## ✨ 功能特性

### 工具信息
- **工具名称**: 商用车标准化测试统计工具
- **版本**: v2.4
- **工具ID**: commercial_vehicle
- **图标**: 卡车图标 (fas fa-truck)
- **主题色**: 绿色 (success)

### 支持的启动方式
1. **客户端启动**（推荐）
   - 下载EXE程序到用户本地电脑
   - 在用户电脑上独立运行
   - 不占用服务器资源
   - 支持离线使用

2. **服务器端启动**
   - 在服务器电脑上运行
   - 需要到服务器前操作
   - 占用服务器资源

## 🖥️ 用户界面

### 仪表板显示
- 工具卡片采用绿色主题
- 卡车图标清晰标识
- 与其他工具并列显示
- 统一的操作体验

### 工具选择页面
- 专门的商用车工具介绍
- 两种启动方式的详细对比
- 清晰的使用说明
- 文件下载和启动功能

## 🔧 技术实现

### 配置映射
```python
# 工具配置
{
    'id': 'commercial_vehicle',
    'name': '商用车标准化测试统计工具',
    'description': '点击直接启动商用车标准化测试统计工具GUI',
    'icon': 'fas fa-truck',
    'color': 'success'
}

# EXE路径映射
exe_paths = {
    'commercial_vehicle': '../exe_programs/商用车标准化测试统计工具v2.4.exe'
}

# 工具名称映射
tool_names = {
    'commercial_vehicle': '商用车标准化测试统计工具'
}
```

### 路由支持
- `/tool/commercial_vehicle` - 工具选择界面
- `/download/commercial_vehicle` - 下载EXE程序
- `/launch_local/commercial_vehicle` - 服务器端启动

## 📊 功能对比

| 特性 | C分析工具 | Java分析工具 | 商用车工具 |
|------|-----------|--------------|------------|
| 工具类型 | 代码分析 | 代码分析 | 测试统计 |
| 主要用途 | CPU/内存分析 | 性能分析 | 测试数据统计 |
| 图标 | 代码图标 | Java图标 | 卡车图标 |
| 主题色 | 蓝色 | 黄色 | 绿色 |
| 启动方式 | 双模式 | 双模式 | 双模式 |

## 🚀 使用流程

### 客户端启动流程
1. 登录Web平台
2. 在仪表板中找到"商用车标准化测试统计工具"
3. 点击"选择启动方式"
4. 选择"客户端启动"
5. 点击"下载到本地"
6. 保存`商用车标准化测试统计工具.exe`到电脑
7. 双击运行工具

### 服务器端启动流程
1. 登录Web平台
2. 点击商用车工具
3. 选择"服务器端启动"
4. 确认启动操作
5. 到服务器电脑前操作

## 📁 文件信息

### EXE程序
- **文件名**: 商用车标准化测试统计工具v2.4.exe
- **位置**: exe_programs/商用车标准化测试统计工具v2.4.exe
- **大小**: 约50-100MB（具体大小取决于依赖）
- **系统要求**: Windows 7/8/10/11

### 下载文件名
- 下载时文件名: `商用车标准化测试统计工具.exe`
- 保持简洁的文件名，便于用户识别

## 🎯 应用场景

### 商用车测试
- 标准化测试数据收集
- 测试结果统计分析
- 测试报告生成
- 数据可视化展示

### 质量控制
- 测试数据验证
- 标准符合性检查
- 异常数据识别
- 趋势分析

### 研发支持
- 测试数据分析
- 性能评估
- 改进建议
- 数据对比

## ⚠️ 注意事项

### 使用建议
- 推荐使用客户端启动方式
- 首次运行可能需要几秒钟加载
- 确保有足够的磁盘空间
- 建议定期更新工具版本

### 兼容性
- 支持Windows系统
- 无需额外安装依赖
- 独立运行，不影响其他程序
- 支持多实例运行

### 安全性
- EXE程序经过数字签名
- 无恶意代码
- 可能被杀毒软件误报，需添加信任
- 支持离线运行

## 🔄 版本管理

### 当前版本
- **版本号**: v2.4
- **发布日期**: 根据原始工具更新
- **更新内容**: 保持与原始工具同步

### 版本更新
- 当原始工具更新时，需要重新打包EXE
- 更新EXE文件到exe_programs目录
- 如需要，更新配置中的文件名
- 测试新版本的功能完整性

## 📈 监控和统计

### 使用统计
- 用户下载次数
- 启动方式偏好
- 使用频率统计
- 错误日志记录

### 性能监控
- 启动时间
- 资源占用
- 稳定性监控
- 用户反馈收集

## 🎉 扩展完成

商用车标准化测试统计工具模块已成功集成到平台中，具备以下特点：

### ✅ 完整功能
- 与现有工具完全一致的功能
- 支持客户端和服务器端两种启动方式
- 完整的下载和启动流程
- 统一的用户界面和体验

### ✅ 技术集成
- 完整的路由配置
- 正确的EXE路径映射
- 统一的错误处理
- 完善的进程管理

### ✅ 用户体验
- 清晰的工具标识（卡车图标）
- 专业的绿色主题
- 详细的使用说明
- 友好的操作界面

现在用户可以通过Web平台方便地访问和使用商用车标准化测试统计工具了！
