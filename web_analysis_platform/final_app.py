#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工具平台 - 最终版本
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os
import subprocess
from functools import wraps

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 用户认证
USERS = {'lcj': '123'}

def check_credentials(username, password):
    return username in USERS and USERS[username] == password

def require_login(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if check_credentials(username, password):
            session['logged_in'] = True
            session['username'] = username
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis',
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        }
    ]
    return render_template('dashboard.html', tools=tools)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    # 脚本路径映射
    script_paths = {
        'c_analysis': '../C标准分析工具UI.py',
        'java_analysis': '../Java标准化分析工具UI.py'
    }
    
    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }
    
    if tool_id not in script_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))
    
    script_path = script_paths[tool_id]
    tool_name = tool_names[tool_id]
    
    # 解析绝对路径
    if not os.path.isabs(script_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, script_path))
    else:
        abs_path = script_path
    
    # 调试信息
    app.logger.info(f"尝试启动工具: {tool_name}")
    app.logger.info(f"脚本路径: {abs_path}")
    app.logger.info(f"文件存在: {os.path.exists(abs_path)}")
    
    if not os.path.exists(abs_path):
        flash(f'脚本文件不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))
    
    try:
        # 启动脚本
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(['python', abs_path], 
                            cwd=os.path.dirname(abs_path),
                            creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            process = subprocess.Popen(['python', abs_path], 
                            cwd=os.path.dirname(abs_path))
        
        flash(f'{tool_name} 已启动！进程ID: {process.pid}', 'success')
        app.logger.info(f"成功启动工具，进程ID: {process.pid}")
        return redirect(url_for('dashboard'))
        
    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        app.logger.error(f"启动失败: {str(e)}")
        return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print("访问地址: http://localhost:5000")
    print("登录账号: lcj")
    print("登录密码: 123")
    print("=" * 60)
    
    # 检查脚本文件是否存在
    scripts = {
        'C标准分析工具UI.py': '../C标准分析工具UI.py',
        'Java标准化分析工具UI.py': '../Java标准化分析工具UI.py'
    }
    
    print("检查脚本文件...")
    for name, path in scripts.items():
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, path))
        if os.path.exists(abs_path):
            print(f"✓ {name}: {abs_path}")
        else:
            print(f"✗ {name}: {abs_path} (不存在)")
    
    print("=" * 60)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
