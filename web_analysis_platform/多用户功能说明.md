# 多用户功能说明

## 🎯 功能概述

分析工具平台现已支持多用户同时登录使用，每个用户拥有独立的会话，互不影响。

## 👥 用户账户

### 预设账户
- **lcj** / 123 (管理员)
- **admin** / admin123 (管理员)
- **user1** / password1 (普通用户)
- **user2** / password2 (普通用户)
- **test** / test123 (测试用户)

### 权限说明
- **管理员** (lcj, admin): 可访问管理页面，查看所有用户会话
- **普通用户**: 只能使用分析工具，无法访问管理功能

## 🔧 核心功能

### 1. 独立会话管理
- ✅ 每个用户拥有独立的会话ID
- ✅ 会话信息包含登录时间、最后活动时间
- ✅ 自动跟踪用户启动的进程
- ✅ 线程安全的会话管理

### 2. 多用户并发支持
- ✅ 支持多个用户同时登录
- ✅ 用户之间互不影响
- ✅ 独立的工具启动和进程管理
- ✅ 实时显示在线用户数量

### 3. 管理功能
- ✅ 管理员可查看所有活跃会话
- ✅ 显示用户登录时间和活动状态
- ✅ 查看每个用户启动的进程详情
- ✅ 实时统计在线用户和运行进程

## 🖥️ 用户界面

### 登录页面
- 显示支持多用户登录提示
- 提供测试账户信息
- 友好的登录界面

### 仪表板
- 个性化欢迎信息
- 显示当前用户信息
- 实时显示在线用户数量
- 管理员可看到"管理"按钮

### 管理页面 (仅管理员)
- 活跃用户统计
- 详细的会话列表
- 进程运行状态
- 自动刷新功能

## 🔒 安全特性

### 会话安全
- 独立的会话ID (UUID)
- 会话超时管理
- 线程安全的数据访问
- 自动清理过期会话

### 权限控制
- 基于用户名的权限验证
- 管理页面访问控制
- 安全的登录装饰器

## 📊 技术实现

### 会话管理
```python
# 会话数据结构
active_sessions = {
    'session_id': {
        'username': 'lcj',
        'login_time': datetime.now(),
        'last_activity': datetime.now(),
        'processes': [
            {
                'tool_name': 'C标准化分析工具',
                'pid': 12345,
                'start_time': datetime.now()
            }
        ]
    }
}
```

### 并发安全
- 使用 `threading.Lock()` 保证线程安全
- 原子操作更新会话信息
- 安全的会话创建和删除

### 进程跟踪
- 记录用户启动的每个工具进程
- 显示进程ID和启动时间
- 按用户分组管理进程

## 🚀 使用场景

### 多人协作
- 团队成员可同时使用不同的分析工具
- 每个人的工作互不干扰
- 管理员可监控整体使用情况

### 教学环境
- 老师可以监控学生的使用情况
- 学生可以独立使用工具
- 支持多个学生同时操作

### 企业环境
- 不同部门员工可同时访问
- 管理员可以查看使用统计
- 支持高并发访问

## ⚠️ 注意事项

### 资源管理
- 每个用户启动的工具会消耗系统资源
- 建议监控系统性能
- 必要时限制同时在线用户数

### 会话清理
- 系统会自动跟踪用户活动
- 长时间不活跃的会话可能需要手动清理
- 建议定期重启服务清理资源

### 安全建议
- 定期更改默认密码
- 监控异常登录活动
- 备份重要的会话数据

## 📈 监控指标

管理页面提供以下监控信息：
- **在线用户数**: 当前活跃的用户会话
- **活跃会话数**: 有运行进程的会话
- **运行进程数**: 所有用户启动的工具进程总数
- **总会话数**: 系统中的所有会话

## 🎉 使用效果

用户体验：
1. 多个用户可以同时访问 http://localhost:5000
2. 使用不同账户登录，互不冲突
3. 每个用户可以独立启动分析工具
4. 管理员可以实时监控所有用户状态
5. 系统自动管理会话和进程信息

这个多用户系统为分析工具平台提供了企业级的用户管理能力，支持团队协作和集中管理。
