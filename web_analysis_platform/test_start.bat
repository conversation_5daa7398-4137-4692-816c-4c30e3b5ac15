@echo off
chcp 65001 >nul
echo 测试Flask应用启动...
echo.

cd /d "%~dp0"

echo 当前目录: %CD%
echo.

echo 测试Python环境...
python --version
if errorlevel 1 (
    echo 错误：Python未找到
    pause
    exit /b 1
)

echo.
echo 测试Flask导入...
python -c "import flask; print('Flask版本:', flask.__version__)"
if errorlevel 1 (
    echo 错误：Flask导入失败
    pause
    exit /b 1
)

echo.
echo 启动简化版应用...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo.

python simple_app.py

echo.
echo 应用已停止
pause
