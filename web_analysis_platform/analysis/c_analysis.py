#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C标准分析工具网页版 - 完全复刻原始脚本功能
"""

import json
import statistics
from datetime import datetime
import os
import re


class CStandardAnalysisWeb:
    """C标准分析工具网页版 - 完全复刻原始脚本"""
    
    def __init__(self):
        # 存储分析结果
        self.cpu_data = None
        self.memory_data = None
        self.performance_data = None
    
    def auto_fill_files(self, directory):
        """自动识别目录下的三类结果文件 - 完全复刻原始逻辑"""
        cpu_pattern = re.compile(r'stability_CPU_\d{8}_\d{6}\.txt')
        detect_pattern = re.compile(r'stability_Detect_\d{8}_\d{6}\.txt')
        memory_pattern = re.compile(r'stability_Memory_\d{8}_\d{6}\.txt')
        
        result_files = {
            'cpu': None,
            'memory': None,
            'performance': None
        }
        
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if cpu_pattern.match(filename):
                result_files['cpu'] = filepath
            elif detect_pattern.match(filename):
                result_files['performance'] = filepath
            elif memory_pattern.match(filename):
                result_files['memory'] = filepath
        
        return result_files
    
    def analyze_cpu(self, file_path):
        """分析CPU数据 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            times = []
            cpu_usages = []
            filtered_cpu_usages = []  # 仅用于计算g_stageStability=3的数据
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        current_cpu = data.get("current_CPU")
                        time_str = data.get("time", "")
                        g_stageStability = data.get("g_stageStability")
                        
                        if current_cpu is not None and time_str:
                            cpu = current_cpu  # 直接使用原始值，不乘以100
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            cpu_usages.append(cpu)
                            if g_stageStability == 3:
                                filtered_cpu_usages.append(cpu)
                    except Exception:
                        continue
            
            if not times:
                return {'success': False, 'message': '没有有效的CPU数据'}
            
            # 计算统计信息（仅使用g_stageStability=3的数据）
            if filtered_cpu_usages:
                avg_cpu = statistics.mean(filtered_cpu_usages)
                max_cpu = max(filtered_cpu_usages)
                min_cpu = min(filtered_cpu_usages)
            else:
                avg_cpu = max_cpu = min_cpu = 0
            
            # 生成图表数据
            chart_data = {
                'type': 'line',
                'data': {
                    'labels': [t.strftime('%H:%M:%S') for t in times],
                    'datasets': [{
                        'label': 'CPU 使用率 (%)',
                        'data': cpu_usages,
                        'borderColor': 'red',
                        'backgroundColor': 'rgba(255, 0, 0, 0.1)',
                        'tension': 0.1,
                        'pointRadius': 3
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': 'CPU 使用随时间变化'
                        },
                        'annotation': {
                            'annotations': {
                                'line1': {
                                    'type': 'line',
                                    'yMin': avg_cpu,
                                    'yMax': avg_cpu,
                                    'borderColor': 'orange',
                                    'borderWidth': 2,
                                    'borderDash': [5, 5],
                                    'label': {
                                        'content': f'g_stageStability=3 时平均CPU使用率: {avg_cpu:.2f}%',
                                        'enabled': True,
                                        'position': 'center'
                                    }
                                }
                            }
                        }
                    },
                    'scales': {
                        'x': {
                            'title': {
                                'display': True,
                                'text': '时间'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': '使用率 (%)'
                            }
                        }
                    }
                }
            }
            
            # 生成结果文本
            result_text = f"""CPU分析结果：
数据点数量: {len(cpu_usages)}（全部g_stageStability值）
用于统计的数据点数: {len(filtered_cpu_usages)}（仅g_stageStability=3）

平均CPU使用率: {avg_cpu:.2f}%  最大CPU使用率: {max_cpu:.2f}%  最小CPU使用率: {min_cpu:.2f}%
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}"""
            
            return {
                'success': True,
                'chart': chart_data,
                'result_text': result_text,
                'stats': {
                    'avg_cpu': avg_cpu,
                    'max_cpu': max_cpu,
                    'min_cpu': min_cpu,
                    'total_points': len(cpu_usages),
                    'filtered_points': len(filtered_cpu_usages)
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def analyze_memory(self, file_path):
        """分析内存数据 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            times = []
            memory_values = []
            filtered_memory_values = []  # 仅用于计算g_stageStability=3的数据
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        mem_mb = data.get("current_memory")
                        time_str = data.get("time", "")
                        g_stageStability = data.get("g_stageStability")
                        
                        if mem_mb is not None and time_str:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            memory_values.append(mem_mb)
                            if g_stageStability == 3:
                                filtered_memory_values.append(mem_mb)
                    except Exception:
                        continue
            
            if not times:
                return {'success': False, 'message': '没有有效的内存数据'}
            
            # 计算统计信息（仅使用g_stageStability=3的数据）
            if filtered_memory_values:
                avg_mem = statistics.mean(filtered_memory_values)
                max_mem = max(filtered_memory_values)
                min_mem = min(filtered_memory_values)
            else:
                avg_mem = max_mem = min_mem = 0
            
            # 生成图表数据
            chart_data = {
                'type': 'line',
                'data': {
                    'labels': [t.strftime('%H:%M:%S') for t in times],
                    'datasets': [{
                        'label': '内存占用 (MB)',
                        'data': memory_values,
                        'borderColor': 'blue',
                        'backgroundColor': 'rgba(0, 0, 255, 0.1)',
                        'tension': 0.1,
                        'pointRadius': 3
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '内存占用随时间变化'
                        },
                        'annotation': {
                            'annotations': {
                                'line1': {
                                    'type': 'line',
                                    'yMin': avg_mem,
                                    'yMax': avg_mem,
                                    'borderColor': 'orange',
                                    'borderWidth': 2,
                                    'borderDash': [5, 5],
                                    'label': {
                                        'content': f'g_stageStability=3 时平均内存占用: {avg_mem:.2f} MB',
                                        'enabled': True,
                                        'position': 'center'
                                    }
                                }
                            }
                        }
                    },
                    'scales': {
                        'x': {
                            'title': {
                                'display': True,
                                'text': '时间'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': '内存占用 (MB)'
                            }
                        }
                    }
                }
            }
            
            # 生成结果文本
            result_text = f"""内存分析结果：
数据点数量: {len(memory_values)}（全部g_stageStability值）
用于统计的数据点数: {len(filtered_memory_values)}（仅g_stageStability=3）

平均内存占用: {avg_mem:.2f} MB  最大内存占用: {max_mem:.2f} MB  最小内存占用: {min_mem:.2f} MB"""
            
            return {
                'success': True,
                'chart': chart_data,
                'result_text': result_text,
                'stats': {
                    'avg_mem': avg_mem,
                    'max_mem': max_mem,
                    'min_mem': min_mem,
                    'total_points': len(memory_values),
                    'filtered_points': len(filtered_memory_values)
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def analyze_performance(self, file_path):
        """分析性能数据 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            module_data = {}
            filtered_module_data = {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # 跳过前5行，从第6行开始处理
                for line in lines[5:]:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        module = data.get("module", "Unknown")
                        cost_time = data.get("costTime")
                        time_str = data.get("time")
                        g_stageStability = data.get("g_stageStability")
                        
                        if cost_time is not None and time_str:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            # 所有数据都加入module_data用于绘图
                            if module not in module_data:
                                module_data[module] = {"times": [], "costs": []}
                            module_data[module]["times"].append(time_obj)
                            module_data[module]["costs"].append(cost_time)
                            # 仅g_stageStability=3的数据加入filtered_module_data用于统计
                            if g_stageStability == 3:
                                if module not in filtered_module_data:
                                    filtered_module_data[module] = []
                                filtered_module_data[module].append(cost_time)
                    except Exception:
                        continue
            
            if not module_data:
                return {'success': False, 'message': '没有有效的性能数据'}
            
            # 生成图表数据和结果文本
            datasets = []
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            result_text = "=== 多模块性能分析结果 ===\n\n"
            
            for i, (module, data) in enumerate(module_data.items()):
                color = colors[i % len(colors)]
                datasets.append({
                    'label': f'{module} 耗时 (ms)',
                    'data': [{'x': t.strftime('%H:%M:%S'), 'y': cost} for t, cost in zip(data['times'], data['costs'])],
                    'borderColor': color,
                    'backgroundColor': f'rgba({color}, 0.1)',
                    'tension': 0.1,
                    'pointRadius': 3
                })
                
                # 计算统计信息
                if module in filtered_module_data and filtered_module_data[module]:
                    filtered_costs = filtered_module_data[module]
                    avg_cost = statistics.mean(filtered_costs)
                    max_cost = max(filtered_costs)
                    min_cost = min(filtered_costs)
                    result_text += f"模块 {module}:\n"
                    result_text += f"  数据点数量: {len(data['costs'])}（全部g_stageStability值）\n"
                    result_text += f"  用于统计的数据点数: {len(filtered_costs)}（仅g_stageStability=3）\n"
                    result_text += f"  平均耗时: {avg_cost:.2f} ms\n"
                    result_text += f"  最大耗时: {max_cost:.2f} ms\n"
                    result_text += f"  最小耗时: {min_cost:.2f} ms\n\n"
                else:
                    result_text += f"模块 {module}: 无g_stageStability=3的数据用于统计\n\n"
            
            chart_data = {
                'type': 'line',
                'data': {
                    'datasets': datasets
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '多模块性能耗时随时间变化'
                        }
                    },
                    'scales': {
                        'x': {
                            'type': 'category',
                            'title': {
                                'display': True,
                                'text': '时间'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': '耗时 (ms)'
                            }
                        }
                    }
                }
            }
            
            return {
                'success': True,
                'chart': chart_data,
                'result_text': result_text,
                'stats': {
                    'modules': list(module_data.keys()),
                    'total_modules': len(module_data)
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def batch_analyze(self, directory):
        """批量分析 - 完全复刻原始逻辑"""
        if not os.path.exists(directory):
            return {'success': False, 'message': '目录不存在'}
        
        # 自动填充文件
        found_files = self.auto_fill_files(directory)
        
        missing = [key for key, path in found_files.items() if not path or not os.path.exists(path)]
        if missing:
            return {'success': False, 'message': f'未找到以下类型的文件: {", ".join(missing)}'}
        
        results = {}
        
        # 分析CPU数据
        if found_files['cpu']:
            results['cpu'] = self.analyze_cpu(found_files['cpu'])
        
        # 分析内存数据
        if found_files['memory']:
            results['memory'] = self.analyze_memory(found_files['memory'])
        
        # 分析性能数据
        if found_files['performance']:
            results['performance'] = self.analyze_performance(found_files['performance'])
        
        return {
            'success': True,
            'results': results,
            'found_files': found_files,
            'message': '批量分析已完成！'
        }
