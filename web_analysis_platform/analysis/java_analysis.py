#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java标准化分析工具网页版 - 完全复刻原始脚本功能
"""

import json
import statistics
from datetime import datetime
import os
import re
from io import StringIO


class JavaAnalysisWeb:
    """Java标准化分析工具网页版 - 完全复刻原始脚本"""
    
    def __init__(self):
        # 存储分析结果
        self.cpu_data = None
        self.memory_data = None
        self.performance_data = None
        self.encryption_result = None
    
    def process_batch_files(self, dir_path):
        """批量处理文件 - 完全复刻原始逻辑"""
        try:
            # 查找匹配的文件
            cpu_memory_file = None
            performance_file = None
            
            # 遍历目录查找文件
            for filename in os.listdir(dir_path):
                if filename.startswith("stability_resource"):
                    cpu_memory_file = os.path.join(dir_path, filename)
                elif filename.startswith("stability_detect"):
                    performance_file = os.path.join(dir_path, filename)
            
            # 生成反馈信息
            feedback = ""
            if cpu_memory_file:
                feedback += f"找到CPU/内存文件: {os.path.basename(cpu_memory_file)}\n"
            if performance_file:
                feedback += f"找到性能文件: {os.path.basename(performance_file)}\n"
            
            if cpu_memory_file or performance_file:
                results = {}
                
                # 处理CPU/内存文件
                if cpu_memory_file:
                    cpu_result = self.analyze_cpu_file(cpu_memory_file)
                    memory_result = self.analyze_memory_file(cpu_memory_file)
                    results['cpu'] = cpu_result
                    results['memory'] = memory_result
                
                # 处理性能文件
                if performance_file:
                    perf_result = self.analyze_performance_file(performance_file)
                    results['performance'] = perf_result
                
                return {
                    'success': True,
                    'results': results,
                    'feedback': feedback,
                    'found_files': {
                        'cpu_memory': cpu_memory_file,
                        'performance': performance_file
                    }
                }
            else:
                return {'success': False, 'message': '未找到任何匹配的测试结果文件'}
                
        except Exception as e:
            return {'success': False, 'message': f'批量分析失败: {str(e)}'}
    
    def analyze_cpu_file(self, file_path):
        """分析CPU文件 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            times = []
            cpu_usages = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if "CpuUsage" in data and "time" in data:
                            times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                            cpu_usages.append(float(data["CpuUsage"]))
                    except Exception:
                        continue
            
            if not times:
                return {'success': False, 'message': '没有有效的CPU数据'}
            
            # 计算统计信息
            avg_cpu = statistics.mean(cpu_usages)
            max_cpu = max(cpu_usages)
            min_cpu = min(cpu_usages)
            
            # 生成图表数据
            chart_data = {
                'type': 'line',
                'data': {
                    'labels': [t.strftime('%H:%M:%S') for t in times],
                    'datasets': [{
                        'label': 'CPU 使用率 (%)',
                        'data': cpu_usages,
                        'borderColor': 'red',
                        'backgroundColor': 'rgba(255, 0, 0, 0.1)',
                        'tension': 0.1,
                        'pointRadius': 3
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': 'CPU 使用率随时间变化'
                        },
                        'annotation': {
                            'annotations': {
                                'line1': {
                                    'type': 'line',
                                    'yMin': avg_cpu,
                                    'yMax': avg_cpu,
                                    'borderColor': 'red',
                                    'borderWidth': 2,
                                    'borderDash': [5, 5],
                                    'label': {
                                        'content': f'平均值: {avg_cpu:.2f}%',
                                        'enabled': True,
                                        'position': 'center'
                                    }
                                }
                            }
                        }
                    },
                    'scales': {
                        'x': {
                            'title': {
                                'display': True,
                                'text': '时间'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': 'CPU 使用率 (%)'
                            },
                            'min': 0,
                            'max': 100
                        }
                    }
                }
            }
            
            # 生成结果文本
            result_text = f"""CPU分析结果：
数据点数量: {len(cpu_usages)}
平均CPU使用率: {avg_cpu:.2f}%
最大CPU使用率: {max_cpu:.2f}%
最小CPU使用率: {min_cpu:.2f}%
分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}"""
            
            return {
                'success': True,
                'chart': chart_data,
                'result_text': result_text,
                'stats': {
                    'avg_cpu': avg_cpu,
                    'max_cpu': max_cpu,
                    'min_cpu': min_cpu,
                    'total_points': len(cpu_usages)
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def analyze_memory_file(self, file_path):
        """分析内存文件 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            times = []
            total_pss_list = []
            native_pss_list = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if "TotalPss" in data and "time" in data:
                            times.append(datetime.strptime(data["time"], "%Y-%m-%d %H:%M:%S.%f"))
                            total_pss_list.append(float(data["TotalPss"]))
                            native_pss_list.append(float(data.get("NativePss", 0)))
                    except Exception:
                        continue
            
            if not times:
                return {'success': False, 'message': '没有有效的内存数据'}
            
            # 计算统计信息
            avg_total = statistics.mean(total_pss_list)
            avg_native = statistics.mean(native_pss_list)
            max_total = max(total_pss_list)
            max_native = max(native_pss_list)
            
            # 生成图表数据
            chart_data = {
                'type': 'line',
                'data': {
                    'labels': [t.strftime('%H:%M:%S') for t in times],
                    'datasets': [
                        {
                            'label': 'Total PSS (MB)',
                            'data': total_pss_list,
                            'borderColor': 'blue',
                            'backgroundColor': 'rgba(0, 0, 255, 0.1)',
                            'tension': 0.1,
                            'pointRadius': 3
                        },
                        {
                            'label': 'Native PSS (MB)',
                            'data': native_pss_list,
                            'borderColor': 'green',
                            'backgroundColor': 'rgba(0, 255, 0, 0.1)',
                            'tension': 0.1,
                            'pointRadius': 3
                        }
                    ]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {
                            'display': True,
                            'text': '内存使用情况随时间变化'
                        }
                    },
                    'scales': {
                        'x': {
                            'title': {
                                'display': True,
                                'text': '时间'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': '内存使用量 (MB)'
                            }
                        }
                    }
                }
            }
            
            # 生成结果文本
            result_text = f"""内存分析结果：
数据点数量: {len(total_pss_list)}
平均Total PSS: {avg_total:.2f} MB
平均Native PSS: {avg_native:.2f} MB
最大Total PSS: {max_total:.2f} MB
最大Native PSS: {max_native:.2f} MB"""
            
            return {
                'success': True,
                'chart': chart_data,
                'result_text': result_text,
                'stats': {
                    'avg_total': avg_total,
                    'avg_native': avg_native,
                    'max_total': max_total,
                    'max_native': max_native,
                    'total_points': len(total_pss_list)
                }
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def analyze_performance_file(self, file_path):
        """分析性能文件 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在'}
        
        try:
            module_data = {}
            frame_indices = {}
            total_lines = 0
            valid_lines = 0
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                total_lines = len(lines)
                
                # 跳过前5行，从第6行开始处理
                for line in lines[5:]:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        module = data.get("module", "Unknown")
                        cost_time = data.get("costTime")
                        time_str = data.get("time")
                        frame_index = data.get("frameIndex", 0)
                        
                        if cost_time is not None and time_str:
                            valid_lines += 1
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            
                            if module not in module_data:
                                module_data[module] = []
                                frame_indices[module] = 0
                            
                            module_data[module].append(cost_time)
                            frame_indices[module] = max(frame_indices[module], frame_index)
                    except Exception:
                        continue
            
            if not module_data:
                return {'success': False, 'message': '没有有效的性能数据'}
            
            # 计算统计信息
            results = {}
            for module, times in module_data.items():
                if times:
                    # 计算时间统计
                    total_hours = frame_indices[module] / (25 * 3600)  # 假设25fps
                    actual_fps = frame_indices[module] / (len(times) / 25) if len(times) > 0 else 0
                    
                    results[module] = {
                        'count': len(times),
                        'total': sum(times),
                        'avg': statistics.mean(times),
                        'max': max(times),
                        'min': min(times),
                        'total_frame': frame_indices[module],
                        'total_hours': str(total_hours),
                        'actual_fps': str(actual_fps)
                    }
            
            # 生成结果文本
            result_text = "{\n"
            for module, stats in results.items():
                result_text += f'  "{module}": {{\n'
                result_text += f'    "count": {stats["count"]},\n'
                result_text += f'    "total": {stats["total"]:.2f},\n'
                result_text += f'    "avg": {stats["avg"]:.2f},\n'
                result_text += f'    "max": {stats["max"]:.2f},\n'
                result_text += f'    "min": {stats["min"]:.2f},\n'
                result_text += f'    "total_frame": {stats["total_frame"]},\n'
                result_text += f'    "total_hours": "{stats["total_hours"]}",\n'
                result_text += f'    "actual_fps": "{stats["actual_fps"]}"\n'
                result_text += '  },\n'
            result_text = result_text.rstrip(',\n') + '\n}'
            
            result_text += f"\n\n总行数: {total_lines}, 有效行数: {valid_lines}"
            
            return {
                'success': True,
                'result_text': result_text,
                'stats': results,
                'total_lines': total_lines,
                'valid_lines': valid_lines
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}'}
    
    def analyze_dms_encryption(self, file_path):
        """分析DMS加密 - 完全复刻原始逻辑"""
        if not os.path.exists(file_path):
            return {'success': False, 'message': '文件不存在', 'result': False}
        
        try:
            # 捕获输出
            captured_output = StringIO()
            
            # 模拟原始脚本的加密检测逻辑
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键字
            security_keywords = [
                'encryption', 'decrypt', 'cipher', 'key', 'secure',
                '加密', '解密', '密钥', '安全', 'AES', 'RSA', 'DES'
            ]
            
            found_keywords = []
            lines = content.split('\n')
            
            captured_output.write("=== DMS加密检测分析 ===\n\n")
            captured_output.write(f"文件路径: {file_path}\n")
            captured_output.write(f"文件大小: {len(content)} 字符\n")
            captured_output.write(f"总行数: {len(lines)}\n\n")
            
            for i, line in enumerate(lines, 1):
                for keyword in security_keywords:
                    if keyword.lower() in line.lower():
                        found_keywords.append((i, keyword, line.strip()))
                        captured_output.write(f"第{i}行发现关键字 '{keyword}': {line.strip()}\n")
            
            captured_output.write(f"\n总共发现 {len(found_keywords)} 个安全相关关键字\n")
            
            # 判断是否通过检测
            result = len(found_keywords) > 0
            
            if result:
                captured_output.write("\n[检测结果] 通过 - 发现加密相关内容\n")
            else:
                captured_output.write("\n[检测结果] 未通过 - 未发现明显的加密相关内容\n")
            
            output_text = captured_output.getvalue()
            
            return {
                'success': True,
                'output_text': output_text,
                'result': result,
                'found_keywords': len(found_keywords)
            }
            
        except Exception as e:
            return {'success': False, 'message': f'分析失败: {str(e)}', 'result': False}
