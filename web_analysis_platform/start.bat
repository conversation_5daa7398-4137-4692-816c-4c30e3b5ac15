@echo off
chcp 65001 >nul
echo ============================================================
echo                   分析工具平台启动脚本
echo ============================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show Flask >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo ============================================================
echo 启动信息：
echo 访问地址：http://localhost:5000
echo 登录账号：lcj
echo 登录密码：123
echo ============================================================
echo.
echo 正在启动Web服务器...
echo 按 Ctrl+C 可以停止服务器
echo.

python run.py

echo.
echo 服务器已停止
pause
