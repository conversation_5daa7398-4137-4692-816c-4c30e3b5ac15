#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工具平台 - 主应用
直接启动C和Java分析工具的Web平台
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, send_file, jsonify
import os
import subprocess
import uuid
import threading
import psutil
import atexit
import signal
from functools import wraps
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 用户会话管理
active_sessions = {}  # 存储活跃会话信息
session_lock = threading.Lock()  # 线程锁保证并发安全
all_processes = []  # 存储所有启动的进程，用于清理

# 用户认证配置 - 支持多用户
USERS = {
    'lcj': '123',
    'admin': 'admin123',
    'user1': 'password1',
    'user2': 'password2',
    'test': 'test123'
}

def check_credentials(username, password):
    """检查用户凭据"""
    return username in USERS and USERS[username] == password

def create_user_session(username):
    """创建用户会话"""
    session_id = str(uuid.uuid4())
    with session_lock:
        active_sessions[session_id] = {
            'username': username,
            'login_time': datetime.now(),
            'last_activity': datetime.now(),
            'processes': []  # 存储该用户启动的进程
        }
    return session_id

def update_user_activity(session_id):
    """更新用户活动时间"""
    with session_lock:
        if session_id in active_sessions:
            active_sessions[session_id]['last_activity'] = datetime.now()

def get_user_session(session_id):
    """获取用户会话信息"""
    with session_lock:
        return active_sessions.get(session_id)

def remove_user_session(session_id):
    """移除用户会话"""
    with session_lock:
        if session_id in active_sessions:
            # 清理该用户的所有进程
            cleanup_user_processes(session_id)
            del active_sessions[session_id]

def cleanup_user_processes(session_id):
    """清理指定用户的所有进程"""
    user_session = active_sessions.get(session_id)
    if user_session and user_session['processes']:
        for process_info in user_session['processes']:
            try:
                pid = process_info['pid']
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process.terminate()  # 优雅终止
                    print(f"已终止进程 {pid} ({process_info['tool_name']})")
            except Exception as e:
                print(f"终止进程失败 {process_info['pid']}: {e}")
        user_session['processes'].clear()

def cleanup_all_processes():
    """清理所有启动的进程"""
    print("正在清理所有启动的进程...")
    with session_lock:
        for session_id, session_data in active_sessions.items():
            cleanup_user_processes(session_id)

    # 清理全局进程列表
    for pid in all_processes[:]:
        try:
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                process.terminate()
                print(f"已终止进程 {pid}")
        except Exception as e:
            print(f"终止进程失败 {pid}: {e}")
        all_processes.remove(pid)

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"接收到信号 {signum}，正在清理...")
    cleanup_all_processes()
    exit(0)

def require_login(f):
    """登录装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session or 'session_id' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))

        # 更新用户活动时间
        update_user_activity(session['session_id'])
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """首页 - 重定向到登录或仪表板"""
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if check_credentials(username, password):
            # 创建用户会话
            session_id = create_user_session(username)

            session['logged_in'] = True
            session['username'] = username
            session['session_id'] = session_id
            session.permanent = True

            flash(f'欢迎 {username}！登录成功', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """登出"""
    # 移除用户会话
    if 'session_id' in session:
        remove_user_session(session['session_id'])

    username = session.get('username', '用户')
    session.clear()
    flash(f'{username} 已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    """仪表板 - 显示所有可用工具"""
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis',
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        },
        {
            'id': 'commercial_vehicle',
            'name': '商用车标准化测试统计工具',
            'description': '点击直接启动商用车标准化测试统计工具GUI',
            'icon': 'fas fa-truck',
            'color': 'success'
        }
    ]

    # 获取当前用户会话信息
    user_session = get_user_session(session['session_id'])
    session_info = {
        'username': session['username'],
        'login_time': user_session['login_time'].strftime('%Y-%m-%d %H:%M:%S') if user_session else '',
        'active_users': len(active_sessions)
    }

    return render_template('dashboard.html', tools=tools, session_info=session_info)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    """工具界面 - 提供客户端下载和启动选项"""

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    if tool_id not in tool_names:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    tool_name = tool_names[tool_id]

    return render_template('tool_interface.html',
                         tool_id=tool_id,
                         tool_name=tool_name)

@app.route('/download/<tool_id>')
@require_login
def download_tool(tool_id):
    """下载EXE程序到客户端"""

    # EXE程序路径映射
    exe_paths = {
        'c_analysis': '../exe_programs/C标准分析工具.exe',
        'java_analysis': '../exe_programs/Java标准化分析工具.exe'
    }

    tool_names = {
        'c_analysis': 'C标准分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    if tool_id not in exe_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    exe_path = exe_paths[tool_id]
    tool_name = tool_names[tool_id]

    # 解析绝对路径
    if not os.path.isabs(exe_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    else:
        abs_path = exe_path

    if not os.path.exists(abs_path):
        flash(f'EXE程序不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))

    try:
        # 记录下载行为
        print(f"用户 {session['username']} 下载了 {tool_name}")

        return send_file(abs_path,
                        as_attachment=True,
                        download_name=f"{tool_name}.exe")

    except Exception as e:
        flash(f'下载失败: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/launch_local/<tool_id>')
@require_login
def launch_local(tool_id):
    """在服务器端启动工具（原有功能保留）"""

    # EXE程序路径映射
    exe_paths = {
        'c_analysis': '../exe_programs/C标准分析工具.exe',
        'java_analysis': '../exe_programs/Java标准化分析工具.exe'
    }

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    if tool_id not in exe_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    exe_path = exe_paths[tool_id]
    tool_name = tool_names[tool_id]

    # 解析绝对路径
    if not os.path.isabs(exe_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    else:
        abs_path = exe_path

    if not os.path.exists(abs_path):
        flash(f'EXE程序不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))

    try:
        # 启动EXE程序（非阻塞方式）
        if os.name == 'nt':  # Windows
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))
        else:  # Linux/Mac
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))

        # 记录用户启动的进程
        user_session = get_user_session(session['session_id'])
        if user_session:
            with session_lock:
                user_session['processes'].append({
                    'tool_name': tool_name,
                    'pid': process.pid,
                    'start_time': datetime.now(),
                    'location': 'server'  # 标记为服务器端启动
                })
                # 同时记录到全局进程列表
                all_processes.append(process.pid)

        flash(f'{tool_name} 已在服务器端启动！进程ID: {process.pid}', 'success')
        print(f"用户 {session['username']} 在服务器端启动了 {tool_name}，进程ID: {process.pid}")
        return redirect(url_for('dashboard'))

    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        print(f"用户 {session['username']} 启动 {tool_name} 失败: {str(e)}")
        return redirect(url_for('dashboard'))

@app.route('/admin')
@require_login
def admin():
    """管理页面 - 显示所有活跃用户（仅管理员可访问）"""
    if session['username'] not in ['lcj', 'admin']:
        flash('权限不足！', 'error')
        return redirect(url_for('dashboard'))

    # 获取所有活跃会话
    with session_lock:
        sessions_info = []
        for session_id, session_data in active_sessions.items():
            sessions_info.append({
                'session_id': session_id[:8] + '...',  # 只显示前8位
                'username': session_data['username'],
                'login_time': session_data['login_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'last_activity': session_data['last_activity'].strftime('%Y-%m-%d %H:%M:%S'),
                'processes_count': len(session_data['processes']),
                'processes': session_data['processes']
            })

    return render_template('admin.html', sessions=sessions_info, total_users=len(sessions_info))

if __name__ == '__main__':
    # 注册信号处理器和退出清理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup_all_processes)

    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print("支持多用户登录:")
    for username in USERS.keys():
        print(f"  用户: {username} / 密码: {USERS[username]}")
    print("=" * 60)
    print("新功能:")
    print("  ✓ 支持客户端下载EXE程序")
    print("  ✓ 支持在用户本地电脑启动工具")
    print("  ✓ 自动清理进程（关闭服务器时）")
    print("=" * 60)

    # 检查EXE程序是否存在
    exe_programs = {
        'C标准分析工具.exe': '../exe_programs/C标准分析工具.exe',
        'Java标准化分析工具.exe': '../exe_programs/Java标准化分析工具.exe'
    }

    print("检查EXE程序...")
    for name, path in exe_programs.items():
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, path))
        if os.path.exists(abs_path):
            size_mb = os.path.getsize(abs_path) / (1024 * 1024)
            print(f"✓ {name}: {abs_path} ({size_mb:.1f} MB)")
        else:
            print(f"✗ {name}: {abs_path} (不存在)")

    print("=" * 60)

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        cleanup_all_processes()
    except Exception as e:
        print(f"服务器错误: {e}")
        cleanup_all_processes()
