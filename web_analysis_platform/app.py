#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Analysis Platform - 主应用
集成多个分析工具的Web平台
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os
import subprocess

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 用户认证配置
USERS = {
    'lcj': '123'
}

def check_credentials(username, password):
    """检查用户凭据"""
    return username in USERS and USERS[username] == password

def require_login(f):
    """登录装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """首页 - 重定向到登录或仪表板"""
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if check_credentials(username, password):
            session['logged_in'] = True
            session['username'] = username
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    flash('已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    """仪表板 - 显示所有可用工具"""
    tools = tool_manager.get_all_tools()
    return render_template('dashboard.html', tools=tools)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    """工具界面 - 直接启动原始脚本"""
    tool = tool_manager.get_tool(tool_id)
    if not tool:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    # 直接启动原始脚本
    try:
        import subprocess
        import os

        # 获取脚本路径
        script_path = tool.script_path
        if not os.path.exists(script_path):
            flash(f'脚本文件不存在: {script_path}', 'error')
            return redirect(url_for('dashboard'))

        # 启动脚本（非阻塞方式）
        subprocess.Popen(['python', script_path],
                        cwd=os.path.dirname(script_path),
                        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

        flash(f'{tool.name} 已启动！', 'success')
        return redirect(url_for('dashboard'))

    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/upload', methods=['POST'])
@require_login
def upload_files():
    """文件上传API"""
    try:
        tool_id = request.form.get('tool_id')
        if not tool_id:
            return jsonify({'success': False, 'message': '缺少工具ID'})
        
        tool = tool_manager.get_tool(tool_id)
        if not tool:
            return jsonify({'success': False, 'message': '工具不存在'})
        
        # 创建任务ID
        task_id = str(uuid.uuid4())
        upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
        os.makedirs(upload_dir, exist_ok=True)
        
        uploaded_files = []
        for file_key in request.files:
            file = request.files[file_key]
            if file and file.filename:
                filename = secure_filename(file.filename)
                filepath = os.path.join(upload_dir, filename)
                file.save(filepath)
                uploaded_files.append({
                    'key': file_key,
                    'filename': filename,
                    'filepath': filepath
                })
        
        if not uploaded_files:
            return jsonify({'success': False, 'message': '没有上传任何文件'})
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'files': uploaded_files,
            'message': f'成功上传 {len(uploaded_files)} 个文件'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@app.route('/api/analyze', methods=['POST'])
@require_login
def start_analysis():
    """启动分析任务"""
    try:
        data = request.get_json()
        tool_id = data.get('tool_id')
        task_id = data.get('task_id')
        
        if not tool_id or not task_id:
            return jsonify({'success': False, 'message': '缺少必要参数'})
        
        tool = tool_manager.get_tool(tool_id)
        if not tool:
            return jsonify({'success': False, 'message': '工具不存在'})
        
        # 在后台线程中启动分析
        def run_analysis():
            try:
                upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
                results_dir = os.path.join(app.config['RESULTS_FOLDER'], task_id)
                os.makedirs(results_dir, exist_ok=True)
                
                # 调用工具进行分析
                result = tool.run_analysis(upload_dir, results_dir, data.get('params', {}))
                
                # 保存结果
                result_file = os.path.join(results_dir, 'result.json')
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                # 保存错误信息
                error_file = os.path.join(app.config['RESULTS_FOLDER'], task_id, 'error.json')
                os.makedirs(os.path.dirname(error_file), exist_ok=True)
                with open(error_file, 'w', encoding='utf-8') as f:
                    json.dump({'error': str(e), 'timestamp': datetime.now().isoformat()}, f)
        
        threading.Thread(target=run_analysis, daemon=True).start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '分析任务已启动，请稍候查看结果'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动分析失败: {str(e)}'})

@app.route('/api/status/<task_id>')
@require_login
def get_task_status(task_id):
    """获取任务状态"""
    try:
        results_dir = os.path.join(app.config['RESULTS_FOLDER'], task_id)
        
        # 检查是否有错误
        error_file = os.path.join(results_dir, 'error.json')
        if os.path.exists(error_file):
            with open(error_file, 'r', encoding='utf-8') as f:
                error_data = json.load(f)
            return jsonify({
                'status': 'error',
                'message': error_data.get('error', '未知错误'),
                'timestamp': error_data.get('timestamp')
            })
        
        # 检查是否有结果
        result_file = os.path.join(results_dir, 'result.json')
        if os.path.exists(result_file):
            with open(result_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            return jsonify({
                'status': 'completed',
                'result': result_data
            })
        
        # 任务进行中
        return jsonify({
            'status': 'running',
            'message': '分析正在进行中...'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取状态失败: {str(e)}'
        })

@app.route('/api/download/<task_id>/<filename>')
@require_login
def download_result(task_id, filename):
    """下载结果文件"""
    try:
        results_dir = os.path.join(app.config['RESULTS_FOLDER'], task_id)
        file_path = os.path.join(results_dir, filename)
        
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        return send_file(file_path, as_attachment=True)
        
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
