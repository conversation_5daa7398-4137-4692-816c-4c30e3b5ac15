#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工具平台 - 主应用
直接启动C和Java分析工具的Web平台
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os
import subprocess
from functools import wraps

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 用户认证配置
USERS = {
    'lcj': '123'
}

def check_credentials(username, password):
    """检查用户凭据"""
    return username in USERS and USERS[username] == password

def require_login(f):
    """登录装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """首页 - 重定向到登录或仪表板"""
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if check_credentials(username, password):
            session['logged_in'] = True
            session['username'] = username
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    flash('已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    """仪表板 - 显示所有可用工具"""
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis',
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        }
    ]
    return render_template('dashboard.html', tools=tools)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    """工具界面 - 直接启动exe程序"""

    # EXE程序路径映射
    exe_paths = {
        'c_analysis': '../exe_programs/C标准分析工具.exe',
        'java_analysis': '../exe_programs/Java标准化分析工具.exe'
    }

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    if tool_id not in exe_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    exe_path = exe_paths[tool_id]
    tool_name = tool_names[tool_id]

    # 解析绝对路径
    if not os.path.isabs(exe_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    else:
        abs_path = exe_path

    print(f"尝试启动工具: {tool_name}")
    print(f"EXE路径: {abs_path}")
    print(f"文件存在: {os.path.exists(abs_path)}")

    if not os.path.exists(abs_path):
        flash(f'EXE程序不存在: {abs_path}', 'error')
        flash('请先运行打包脚本生成EXE程序', 'warning')
        return redirect(url_for('dashboard'))

    try:
        # 启动EXE程序（非阻塞方式）
        if os.name == 'nt':  # Windows
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))
        else:  # Linux/Mac
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))

        flash(f'{tool_name} 已启动！进程ID: {process.pid}', 'success')
        print(f"成功启动EXE程序，进程ID: {process.pid}")
        return redirect(url_for('dashboard'))

    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        print(f"启动失败: {str(e)}")
        return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print(f"登录账号: lcj")
    print(f"登录密码: 123")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5000)
