#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工具平台 - 主应用
直接启动C和Java分析工具的Web平台
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, send_file, jsonify
from werkzeug.utils import secure_filename
import os
import subprocess
import uuid
import threading
import psutil
import atexit
import signal
import json
import statistics
from collections import defaultdict
from functools import wraps
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 确保上传目录存在
os.makedirs('static/uploads', exist_ok=True)

# 用户会话管理
active_sessions = {}  # 存储活跃会话信息
session_lock = threading.Lock()  # 线程锁保证并发安全
all_processes = []  # 存储所有启动的进程，用于清理

# 用户认证配置 - 支持多用户
USERS = {
    'lcj': '123',
    'admin': 'admin123',
    'user1': 'password1',
    'user2': 'password2',
    'test': 'test123'
}

def check_credentials(username, password):
    """检查用户凭据"""
    return username in USERS and USERS[username] == password

def create_user_session(username):
    """创建用户会话"""
    session_id = str(uuid.uuid4())
    with session_lock:
        active_sessions[session_id] = {
            'username': username,
            'login_time': datetime.now(),
            'last_activity': datetime.now(),
            'processes': []  # 存储该用户启动的进程
        }
    return session_id

def update_user_activity(session_id):
    """更新用户活动时间"""
    with session_lock:
        if session_id in active_sessions:
            active_sessions[session_id]['last_activity'] = datetime.now()

def get_user_session(session_id):
    """获取用户会话信息"""
    with session_lock:
        return active_sessions.get(session_id)

def remove_user_session(session_id):
    """移除用户会话"""
    with session_lock:
        if session_id in active_sessions:
            # 清理该用户的所有进程
            cleanup_user_processes(session_id)
            del active_sessions[session_id]

def cleanup_user_processes(session_id):
    """清理指定用户的所有进程"""
    user_session = active_sessions.get(session_id)
    if user_session and user_session['processes']:
        for process_info in user_session['processes']:
            try:
                pid = process_info['pid']
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process.terminate()  # 优雅终止
                    print(f"已终止进程 {pid} ({process_info['tool_name']})")
            except Exception as e:
                print(f"终止进程失败 {process_info['pid']}: {e}")
        user_session['processes'].clear()

def cleanup_all_processes():
    """清理所有启动的进程"""
    print("正在清理所有启动的进程...")
    with session_lock:
        for session_id, session_data in active_sessions.items():
            cleanup_user_processes(session_id)

    # 清理全局进程列表
    for pid in all_processes[:]:
        try:
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                process.terminate()
                print(f"已终止进程 {pid}")
        except Exception as e:
            print(f"终止进程失败 {pid}: {e}")
        all_processes.remove(pid)

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"接收到信号 {signum}，正在清理...")
    cleanup_all_processes()
    exit(0)

def require_login(f):
    """登录装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session or 'session_id' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))

        # 更新用户活动时间
        update_user_activity(session['session_id'])
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """首页 - 重定向到登录或仪表板"""
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if check_credentials(username, password):
            # 创建用户会话
            session_id = create_user_session(username)

            session['logged_in'] = True
            session['username'] = username
            session['session_id'] = session_id
            session.permanent = True

            flash(f'欢迎 {username}！登录成功', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """登出"""
    # 移除用户会话
    if 'session_id' in session:
        remove_user_session(session['session_id'])

    username = session.get('username', '用户')
    session.clear()
    flash(f'{username} 已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    """仪表板 - 显示所有可用工具"""
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis',
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        },
        {
            'id': 'commercial_vehicle',
            'name': '商用车标准化测试统计工具',
            'description': '点击直接启动商用车标准化测试统计工具GUI',
            'icon': 'fas fa-truck',
            'color': 'success'
        }
    ]

    # 获取当前用户会话信息
    user_session = get_user_session(session['session_id'])
    session_info = {
        'username': session['username'],
        'login_time': user_session['login_time'].strftime('%Y-%m-%d %H:%M:%S') if user_session else '',
        'active_users': len(active_sessions)
    }

    return render_template('dashboard.html', tools=tools, session_info=session_info)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    """工具界面 - 提供客户端下载和启动选项"""

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具',
        'commercial_vehicle': '商用车标准化测试统计工具'
    }

    if tool_id not in tool_names:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    tool_name = tool_names[tool_id]

    return render_template('tool_interface.html',
                         tool_id=tool_id,
                         tool_name=tool_name)

@app.route('/download/<tool_id>')
@require_login
def download_tool(tool_id):
    """下载EXE程序到客户端"""

    # EXE程序路径映射
    exe_paths = {
        'c_analysis': '../exe_programs/C标准分析工具.exe',
        'java_analysis': '../exe_programs/Java标准化分析工具.exe'
    }

    tool_names = {
        'c_analysis': 'C标准分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    if tool_id not in exe_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    exe_path = exe_paths[tool_id]
    tool_name = tool_names[tool_id]

    # 解析绝对路径
    if not os.path.isabs(exe_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    else:
        abs_path = exe_path

    if not os.path.exists(abs_path):
        flash(f'EXE程序不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))

    try:
        # 记录下载行为
        print(f"用户 {session['username']} 下载了 {tool_name}")

        return send_file(abs_path,
                        as_attachment=True,
                        download_name=f"{tool_name}.exe")

    except Exception as e:
        flash(f'下载失败: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/launch_local/<tool_id>')
@require_login
def launch_local(tool_id):
    """在服务器端启动工具（原有功能保留）"""

    # EXE程序路径映射
    exe_paths = {
        'c_analysis': '../exe_programs/C标准分析工具.exe',
        'java_analysis': '../exe_programs/Java标准化分析工具.exe',
        'commercial_vehicle': '../exe_programs/商用车标准化测试统计工具v2.4.exe'
    }

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具',
        'commercial_vehicle': '商用车标准化测试统计工具'
    }

    if tool_id not in exe_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))

    exe_path = exe_paths[tool_id]
    tool_name = tool_names[tool_id]

    # 解析绝对路径
    if not os.path.isabs(exe_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    else:
        abs_path = exe_path

    if not os.path.exists(abs_path):
        flash(f'EXE程序不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))

    try:
        # 启动EXE程序（非阻塞方式）
        if os.name == 'nt':  # Windows
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))
        else:  # Linux/Mac
            process = subprocess.Popen([abs_path],
                            cwd=os.path.dirname(abs_path))

        # 记录用户启动的进程
        user_session = get_user_session(session['session_id'])
        if user_session:
            with session_lock:
                user_session['processes'].append({
                    'tool_name': tool_name,
                    'pid': process.pid,
                    'start_time': datetime.now(),
                    'location': 'server'  # 标记为服务器端启动
                })
                # 同时记录到全局进程列表
                all_processes.append(process.pid)

        flash(f'{tool_name} 已在服务器端启动！进程ID: {process.pid}', 'success')
        print(f"用户 {session['username']} 在服务器端启动了 {tool_name}，进程ID: {process.pid}")
        return redirect(url_for('dashboard'))

    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        print(f"用户 {session['username']} 启动 {tool_name} 失败: {str(e)}")
        return redirect(url_for('dashboard'))

@app.route('/admin')
@require_login
def admin():
    """管理页面 - 显示所有活跃用户（仅管理员可访问）"""
    if session['username'] not in ['lcj', 'admin']:
        flash('权限不足！', 'error')
        return redirect(url_for('dashboard'))

    # 获取所有活跃会话
    with session_lock:
        sessions_info = []
        for session_id, session_data in active_sessions.items():
            sessions_info.append({
                'session_id': session_id[:8] + '...',  # 只显示前8位
                'username': session_data['username'],
                'login_time': session_data['login_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'last_activity': session_data['last_activity'].strftime('%Y-%m-%d %H:%M:%S'),
                'processes_count': len(session_data['processes']),
                'processes': session_data['processes']
            })

    return render_template('admin.html', sessions=sessions_info, total_users=len(sessions_info))

@app.route('/web_tool/<tool_id>')
@require_login
def web_tool(tool_id):
    """网页版工具界面"""

    # 只支持C和Java分析工具的网页版
    if tool_id not in ['c_analysis', 'java_analysis']:
        flash('该工具暂不支持网页版！', 'error')
        return redirect(url_for('tool_interface', tool_id=tool_id))

    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }

    tool_name = tool_names[tool_id]

    return render_template('web_tool.html',
                         tool_id=tool_id,
                         tool_name=tool_name)

@app.route('/api/upload_single_file', methods=['POST'])
@require_login
def upload_single_file():
    """单个文件上传API"""
    try:
        tool_id = request.form.get('tool_id')
        file_type = request.form.get('type')

        if not tool_id or tool_id not in ['c_analysis', 'java_analysis']:
            return jsonify({'success': False, 'message': '不支持的工具类型'})

        file = request.files.get('file')
        if not file or not file.filename:
            return jsonify({'success': False, 'message': '没有选择文件'})

        # 创建用户专用的上传目录
        user_dir = os.path.join('static', 'uploads', session['username'])
        os.makedirs(user_dir, exist_ok=True)

        filename = secure_filename(file.filename)
        filepath = os.path.join(user_dir, filename)
        file.save(filepath)

        # 存储文件信息到session
        if 'uploaded_files' not in session:
            session['uploaded_files'] = {}
        session['uploaded_files'][file_type] = {
            'filename': filename,
            'filepath': filepath
        }

        return jsonify({
            'success': True,
            'filepath': filepath,
            'message': f'文件 {filename} 上传成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@app.route('/api/upload_multiple_files', methods=['POST'])
@require_login
def upload_multiple_files():
    """多个文件上传API"""
    try:
        tool_id = request.form.get('tool_id')
        if not tool_id or tool_id not in ['c_analysis', 'java_analysis']:
            return jsonify({'success': False, 'message': '不支持的工具类型'})

        # 创建用户专用的上传目录
        user_dir = os.path.join('static', 'uploads', session['username'])
        os.makedirs(user_dir, exist_ok=True)

        uploaded_files = []
        for file_key in request.files:
            file = request.files[file_key]
            if file and file.filename:
                filename = secure_filename(file.filename)
                filepath = os.path.join(user_dir, filename)
                file.save(filepath)

                uploaded_files.append({
                    'filename': filename,
                    'filepath': filepath
                })

        # 存储批量文件信息到session
        session['batch_files'] = uploaded_files

        return jsonify({
            'success': True,
            'files': uploaded_files,
            'message': f'成功上传 {len(uploaded_files)} 个文件'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@app.route('/api/perform_analysis', methods=['POST'])
@require_login
def perform_analysis():
    """执行单个分析API"""
    try:
        data = request.get_json()
        tool_id = data.get('tool_id')
        analysis_type = data.get('analysis_type')

        if not tool_id or tool_id not in ['c_analysis', 'java_analysis']:
            return jsonify({'success': False, 'message': '不支持的工具类型'})

        # 获取对应的文件
        uploaded_files = session.get('uploaded_files', {})
        if analysis_type not in uploaded_files:
            return jsonify({'success': False, 'message': '请先上传对应的数据文件'})

        file_info = uploaded_files[analysis_type]
        filepath = file_info['filepath']

        if not os.path.exists(filepath):
            return jsonify({'success': False, 'message': '文件不存在，请重新上传'})

        # 根据分析类型执行分析
        results = perform_single_analysis(tool_id, analysis_type, filepath)

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'分析失败: {str(e)}'})

@app.route('/api/perform_batch_analysis', methods=['POST'])
@require_login
def perform_batch_analysis():
    """执行批量分析API"""
    try:
        data = request.get_json()
        tool_id = data.get('tool_id')

        if not tool_id or tool_id not in ['c_analysis', 'java_analysis']:
            return jsonify({'success': False, 'message': '不支持的工具类型'})

        # 获取批量文件
        batch_files = session.get('batch_files', [])
        if not batch_files:
            return jsonify({'success': False, 'message': '请先上传批量分析文件'})

        # 执行批量分析
        results = perform_batch_analysis_logic(tool_id, batch_files)

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'批量分析失败: {str(e)}'})

def analyze_c_files(files, options):
    """C标准化分析工具的分析逻辑"""
    results = {
        'stats': {},
        'charts': {},
        'details': [],
        'downloads': []
    }

    cpu_data = []
    memory_data = []
    performance_data = []

    # 读取和解析文件
    for file_info in files:
        filepath = file_info['filepath']
        filename = file_info['filename']

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 根据文件名类型分类处理
            if 'CPU' in filename or 'cpu' in filename:
                cpu_data.extend(parse_cpu_data(content))
            elif 'Memory' in filename or 'memory' in filename:
                memory_data.extend(parse_memory_data(content))
            elif 'Detect' in filename or 'detect' in filename:
                performance_data.extend(parse_performance_data(content))

        except Exception as e:
            results['details'].append({
                'title': f'文件读取错误: {filename}',
                'content': f'<div class="text-danger">错误: {str(e)}</div>'
            })

    # CPU分析
    if options.get('analyzeCPU', True) and cpu_data:
        cpu_stats = analyze_cpu_usage(cpu_data)
        results['stats'].update(cpu_stats['stats'])
        results['charts']['chart1'] = cpu_stats['chart']
        results['details'].append({
            'title': 'CPU使用率分析',
            'content': cpu_stats['details']
        })

    # 内存分析
    if options.get('analyzeMemory', True) and memory_data:
        memory_stats = analyze_memory_usage(memory_data)
        results['stats'].update(memory_stats['stats'])
        results['charts']['chart2'] = memory_stats['chart']
        results['details'].append({
            'title': '内存使用分析',
            'content': memory_stats['details']
        })

    # 性能分析
    if options.get('analyzePerformance', True) and performance_data:
        perf_stats = analyze_performance_data(performance_data)
        results['stats'].update(perf_stats['stats'])
        results['details'].append({
            'title': '性能检测分析',
            'content': perf_stats['details']
        })

    return results

def parse_cpu_data(content):
    """解析CPU数据"""
    cpu_data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 时间戳 CPU使用率
                parts = line.split()
                if len(parts) >= 2:
                    timestamp = parts[0]
                    cpu_usage = float(parts[1])
                    cpu_data.append({
                        'timestamp': timestamp,
                        'usage': cpu_usage
                    })
            except (ValueError, IndexError):
                continue

    return cpu_data

def parse_memory_data(content):
    """解析内存数据"""
    memory_data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 时间戳 内存使用量(MB)
                parts = line.split()
                if len(parts) >= 2:
                    timestamp = parts[0]
                    memory_usage = float(parts[1])
                    memory_data.append({
                        'timestamp': timestamp,
                        'usage': memory_usage
                    })
            except (ValueError, IndexError):
                continue

    return memory_data

def parse_performance_data(content):
    """解析性能数据"""
    performance_data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 时间戳 检测时间(ms)
                parts = line.split()
                if len(parts) >= 2:
                    timestamp = parts[0]
                    detect_time = float(parts[1])
                    performance_data.append({
                        'timestamp': timestamp,
                        'detect_time': detect_time
                    })
            except (ValueError, IndexError):
                continue

    return performance_data

def analyze_cpu_usage(cpu_data):
    """分析CPU使用率"""
    if not cpu_data:
        return {'stats': {}, 'chart': None, 'details': '无CPU数据'}

    usage_values = [item['usage'] for item in cpu_data]

    # 统计计算
    avg_usage = statistics.mean(usage_values)
    max_usage = max(usage_values)
    min_usage = min(usage_values)

    # 图表数据
    chart_data = {
        'type': 'line',
        'data': {
            'labels': [item['timestamp'] for item in cpu_data[-50:]],  # 最近50个数据点
            'datasets': [{
                'label': 'CPU使用率 (%)',
                'data': [item['usage'] for item in cpu_data[-50:]],
                'borderColor': 'rgb(75, 192, 192)',
                'backgroundColor': 'rgba(75, 192, 192, 0.2)',
                'tension': 0.1
            }]
        },
        'options': {
            'responsive': True,
            'plugins': {
                'title': {
                    'display': True,
                    'text': 'CPU使用率趋势'
                }
            },
            'scales': {
                'y': {
                    'beginAtZero': True,
                    'max': 100
                }
            }
        }
    }

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-4">
            <strong>平均使用率:</strong> {avg_usage:.2f}%
        </div>
        <div class="col-md-4">
            <strong>最大使用率:</strong> {max_usage:.2f}%
        </div>
        <div class="col-md-4">
            <strong>最小使用率:</strong> {min_usage:.2f}%
        </div>
    </div>
    <div class="mt-3">
        <strong>数据点总数:</strong> {len(cpu_data)}
    </div>
    '''

    return {
        'stats': {
            '平均CPU使用率': f'{avg_usage:.1f}%',
            '最大CPU使用率': f'{max_usage:.1f}%'
        },
        'chart': chart_data,
        'details': details
    }

def analyze_memory_usage(memory_data):
    """分析内存使用"""
    if not memory_data:
        return {'stats': {}, 'chart': None, 'details': '无内存数据'}

    usage_values = [item['usage'] for item in memory_data]

    # 统计计算
    avg_usage = statistics.mean(usage_values)
    max_usage = max(usage_values)
    min_usage = min(usage_values)

    # 图表数据
    chart_data = {
        'type': 'line',
        'data': {
            'labels': [item['timestamp'] for item in memory_data[-50:]],
            'datasets': [{
                'label': '内存使用量 (MB)',
                'data': [item['usage'] for item in memory_data[-50:]],
                'borderColor': 'rgb(255, 99, 132)',
                'backgroundColor': 'rgba(255, 99, 132, 0.2)',
                'tension': 0.1
            }]
        },
        'options': {
            'responsive': True,
            'plugins': {
                'title': {
                    'display': True,
                    'text': '内存使用量趋势'
                }
            },
            'scales': {
                'y': {
                    'beginAtZero': True
                }
            }
        }
    }

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-4">
            <strong>平均使用量:</strong> {avg_usage:.2f} MB
        </div>
        <div class="col-md-4">
            <strong>最大使用量:</strong> {max_usage:.2f} MB
        </div>
        <div class="col-md-4">
            <strong>最小使用量:</strong> {min_usage:.2f} MB
        </div>
    </div>
    <div class="mt-3">
        <strong>数据点总数:</strong> {len(memory_data)}
    </div>
    '''

    return {
        'stats': {
            '平均内存使用': f'{avg_usage:.1f} MB',
            '最大内存使用': f'{max_usage:.1f} MB'
        },
        'chart': chart_data,
        'details': details
    }

def analyze_performance_data(performance_data):
    """分析性能数据"""
    if not performance_data:
        return {'stats': {}, 'details': '无性能数据'}

    detect_times = [item['detect_time'] for item in performance_data]

    # 统计计算
    avg_time = statistics.mean(detect_times)
    max_time = max(detect_times)
    min_time = min(detect_times)

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-4">
            <strong>平均检测时间:</strong> {avg_time:.2f} ms
        </div>
        <div class="col-md-4">
            <strong>最大检测时间:</strong> {max_time:.2f} ms
        </div>
        <div class="col-md-4">
            <strong>最小检测时间:</strong> {min_time:.2f} ms
        </div>
    </div>
    <div class="mt-3">
        <strong>检测次数:</strong> {len(performance_data)}
    </div>
    '''

    return {
        'stats': {
            '平均检测时间': f'{avg_time:.1f} ms',
            '检测次数': str(len(performance_data))
        },
        'details': details
    }

def analyze_java_files(files, options):
    """Java标准化分析工具的分析逻辑"""
    results = {
        'stats': {},
        'charts': {},
        'details': [],
        'downloads': []
    }

    cpu_memory_data = []
    performance_data = []
    encryption_data = []

    # 读取和解析文件
    for file_info in files:
        filepath = file_info['filepath']
        filename = file_info['filename']

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 根据文件内容和名称判断类型
            if any(keyword in filename.lower() for keyword in ['cpu', 'memory', '内存']):
                cpu_memory_data.extend(parse_java_cpu_memory_data(content))
            elif any(keyword in filename.lower() for keyword in ['performance', '性能']):
                performance_data.extend(parse_java_performance_data(content))
            elif any(keyword in filename.lower() for keyword in ['encrypt', 'crypto', '加密']):
                encryption_data.extend(parse_java_encryption_data(content))
            else:
                # 尝试自动识别内容类型
                if 'CPU' in content or 'Memory' in content:
                    cpu_memory_data.extend(parse_java_cpu_memory_data(content))
                elif 'performance' in content.lower():
                    performance_data.extend(parse_java_performance_data(content))
                elif 'encrypt' in content.lower():
                    encryption_data.extend(parse_java_encryption_data(content))

        except Exception as e:
            results['details'].append({
                'title': f'文件读取错误: {filename}',
                'content': f'<div class="text-danger">错误: {str(e)}</div>'
            })

    # CPU/内存统计分析
    if options.get('analyzeCPUMemory', True) and cpu_memory_data:
        cpu_memory_stats = analyze_java_cpu_memory(cpu_memory_data)
        results['stats'].update(cpu_memory_stats['stats'])
        results['charts']['chart1'] = cpu_memory_stats['chart']
        results['details'].append({
            'title': 'CPU/内存统计分析',
            'content': cpu_memory_stats['details']
        })

    # 性能模块分析
    if options.get('analyzePerformanceModule', True) and performance_data:
        perf_stats = analyze_java_performance(performance_data)
        results['stats'].update(perf_stats['stats'])
        results['charts']['chart2'] = perf_stats['chart']
        results['details'].append({
            'title': '性能模块分析',
            'content': perf_stats['details']
        })

    # 加密检测分析
    if options.get('analyzeEncryption', True) and encryption_data:
        encrypt_stats = analyze_java_encryption(encryption_data)
        results['stats'].update(encrypt_stats['stats'])
        results['details'].append({
            'title': '加密检测分析',
            'content': encrypt_stats['details']
        })

    # 生成报告
    if options.get('generateReport', True):
        report_content = generate_java_report(results)
        results['details'].append({
            'title': '分析报告',
            'content': report_content
        })

    return results

def parse_java_cpu_memory_data(content):
    """解析Java CPU/内存数据"""
    data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 时间戳 CPU使用率 内存使用量
                parts = line.split()
                if len(parts) >= 3:
                    timestamp = parts[0]
                    cpu_usage = float(parts[1])
                    memory_usage = float(parts[2])
                    data.append({
                        'timestamp': timestamp,
                        'cpu': cpu_usage,
                        'memory': memory_usage
                    })
            except (ValueError, IndexError):
                continue

    return data

def parse_java_performance_data(content):
    """解析Java性能数据"""
    data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 模块名 执行时间 状态
                parts = line.split()
                if len(parts) >= 3:
                    module = parts[0]
                    exec_time = float(parts[1])
                    status = parts[2]
                    data.append({
                        'module': module,
                        'exec_time': exec_time,
                        'status': status
                    })
            except (ValueError, IndexError):
                continue

    return data

def parse_java_encryption_data(content):
    """解析Java加密数据"""
    data = []
    lines = content.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            try:
                # 假设格式为: 时间戳 加密算法 检测结果
                parts = line.split()
                if len(parts) >= 3:
                    timestamp = parts[0]
                    algorithm = parts[1]
                    result = parts[2]
                    data.append({
                        'timestamp': timestamp,
                        'algorithm': algorithm,
                        'result': result
                    })
            except (ValueError, IndexError):
                continue

    return data

def analyze_java_cpu_memory(data):
    """分析Java CPU/内存数据"""
    if not data:
        return {'stats': {}, 'chart': None, 'details': '无CPU/内存数据'}

    cpu_values = [item['cpu'] for item in data]
    memory_values = [item['memory'] for item in data]

    # 统计计算
    avg_cpu = statistics.mean(cpu_values)
    avg_memory = statistics.mean(memory_values)
    max_cpu = max(cpu_values)
    max_memory = max(memory_values)

    # 图表数据
    chart_data = {
        'type': 'line',
        'data': {
            'labels': [item['timestamp'] for item in data[-50:]],
            'datasets': [
                {
                    'label': 'CPU使用率 (%)',
                    'data': [item['cpu'] for item in data[-50:]],
                    'borderColor': 'rgb(75, 192, 192)',
                    'backgroundColor': 'rgba(75, 192, 192, 0.2)',
                    'yAxisID': 'y'
                },
                {
                    'label': '内存使用量 (MB)',
                    'data': [item['memory'] for item in data[-50:]],
                    'borderColor': 'rgb(255, 99, 132)',
                    'backgroundColor': 'rgba(255, 99, 132, 0.2)',
                    'yAxisID': 'y1'
                }
            ]
        },
        'options': {
            'responsive': True,
            'plugins': {
                'title': {
                    'display': True,
                    'text': 'CPU/内存使用趋势'
                }
            },
            'scales': {
                'y': {
                    'type': 'linear',
                    'display': True,
                    'position': 'left',
                    'beginAtZero': True,
                    'max': 100
                },
                'y1': {
                    'type': 'linear',
                    'display': True,
                    'position': 'right',
                    'beginAtZero': True
                }
            }
        }
    }

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-6">
            <h6>CPU统计</h6>
            <p><strong>平均使用率:</strong> {avg_cpu:.2f}%</p>
            <p><strong>最大使用率:</strong> {max_cpu:.2f}%</p>
        </div>
        <div class="col-md-6">
            <h6>内存统计</h6>
            <p><strong>平均使用量:</strong> {avg_memory:.2f} MB</p>
            <p><strong>最大使用量:</strong> {max_memory:.2f} MB</p>
        </div>
    </div>
    <div class="mt-3">
        <strong>数据点总数:</strong> {len(data)}
    </div>
    '''

    return {
        'stats': {
            '平均CPU使用率': f'{avg_cpu:.1f}%',
            '平均内存使用': f'{avg_memory:.1f} MB'
        },
        'chart': chart_data,
        'details': details
    }

def analyze_java_performance(data):
    """分析Java性能数据"""
    if not data:
        return {'stats': {}, 'chart': None, 'details': '无性能数据'}

    exec_times = [item['exec_time'] for item in data]
    modules = list(set([item['module'] for item in data]))

    # 统计计算
    avg_time = statistics.mean(exec_times)
    max_time = max(exec_times)
    min_time = min(exec_times)

    # 按模块统计
    module_stats = defaultdict(list)
    for item in data:
        module_stats[item['module']].append(item['exec_time'])

    # 图表数据 - 按模块显示平均执行时间
    module_avg_times = {module: statistics.mean(times) for module, times in module_stats.items()}

    chart_data = {
        'type': 'bar',
        'data': {
            'labels': list(module_avg_times.keys()),
            'datasets': [{
                'label': '平均执行时间 (ms)',
                'data': list(module_avg_times.values()),
                'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                'borderColor': 'rgba(54, 162, 235, 1)',
                'borderWidth': 1
            }]
        },
        'options': {
            'responsive': True,
            'plugins': {
                'title': {
                    'display': True,
                    'text': '各模块平均执行时间'
                }
            },
            'scales': {
                'y': {
                    'beginAtZero': True
                }
            }
        }
    }

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-4">
            <strong>平均执行时间:</strong> {avg_time:.2f} ms
        </div>
        <div class="col-md-4">
            <strong>最大执行时间:</strong> {max_time:.2f} ms
        </div>
        <div class="col-md-4">
            <strong>最小执行时间:</strong> {min_time:.2f} ms
        </div>
    </div>
    <div class="mt-3">
        <strong>模块总数:</strong> {len(modules)}<br>
        <strong>执行记录总数:</strong> {len(data)}
    </div>
    <div class="mt-3">
        <h6>各模块统计:</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>模块名</th>
                        <th>平均时间(ms)</th>
                        <th>执行次数</th>
                    </tr>
                </thead>
                <tbody>
    '''

    for module, times in module_stats.items():
        avg_module_time = statistics.mean(times)
        details += f'''
                    <tr>
                        <td>{module}</td>
                        <td>{avg_module_time:.2f}</td>
                        <td>{len(times)}</td>
                    </tr>
        '''

    details += '''
                </tbody>
            </table>
        </div>
    </div>
    '''

    return {
        'stats': {
            '平均执行时间': f'{avg_time:.1f} ms',
            '模块总数': str(len(modules))
        },
        'chart': chart_data,
        'details': details
    }

def analyze_java_encryption(data):
    """分析Java加密数据"""
    if not data:
        return {'stats': {}, 'details': '无加密数据'}

    algorithms = list(set([item['algorithm'] for item in data]))
    results = [item['result'] for item in data]

    # 统计结果
    result_counts = defaultdict(int)
    for result in results:
        result_counts[result] += 1

    # 按算法统计
    algorithm_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'failed': 0})
    for item in data:
        algorithm_stats[item['algorithm']]['total'] += 1
        if item['result'].lower() in ['success', 'ok', '成功']:
            algorithm_stats[item['algorithm']]['success'] += 1
        else:
            algorithm_stats[item['algorithm']]['failed'] += 1

    # 详细信息
    details = f'''
    <div class="row">
        <div class="col-md-6">
            <h6>检测统计</h6>
            <p><strong>算法总数:</strong> {len(algorithms)}</p>
            <p><strong>检测总次数:</strong> {len(data)}</p>
        </div>
        <div class="col-md-6">
            <h6>结果统计</h6>
    '''

    for result, count in result_counts.items():
        details += f'<p><strong>{result}:</strong> {count} 次</p>'

    details += '''
        </div>
    </div>
    <div class="mt-3">
        <h6>各算法检测结果:</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>算法</th>
                        <th>总次数</th>
                        <th>成功</th>
                        <th>失败</th>
                        <th>成功率</th>
                    </tr>
                </thead>
                <tbody>
    '''

    for algorithm, stats in algorithm_stats.items():
        success_rate = (stats['success'] / stats['total']) * 100 if stats['total'] > 0 else 0
        details += f'''
                    <tr>
                        <td>{algorithm}</td>
                        <td>{stats['total']}</td>
                        <td>{stats['success']}</td>
                        <td>{stats['failed']}</td>
                        <td>{success_rate:.1f}%</td>
                    </tr>
        '''

    details += '''
                </tbody>
            </table>
        </div>
    </div>
    '''

    return {
        'stats': {
            '检测算法数': str(len(algorithms)),
            '检测总次数': str(len(data))
        },
        'details': details
    }

def generate_java_report(results):
    """生成Java分析报告"""
    report = '''
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-file-alt me-2"></i>Java标准化分析报告</h5>
        </div>
        <div class="card-body">
            <h6>分析概要</h6>
            <p>本报告基于上传的Java应用数据文件生成，包含CPU/内存使用情况、性能模块分析和加密检测结果。</p>

            <h6>主要指标</h6>
            <div class="row">
    '''

    for key, value in results['stats'].items():
        report += f'''
                <div class="col-md-3 mb-2">
                    <div class="border p-2 text-center">
                        <strong>{key}</strong><br>
                        <span class="text-primary">{value}</span>
                    </div>
                </div>
        '''

    report += '''
            </div>

            <h6 class="mt-4">分析建议</h6>
            <ul>
                <li>定期监控CPU和内存使用情况，确保系统稳定运行</li>
                <li>关注性能模块的执行时间，优化响应较慢的模块</li>
                <li>验证加密算法的检测结果，确保安全性</li>
                <li>建立性能基线，便于后续对比分析</li>
            </ul>

            <div class="mt-3">
                <small class="text-muted">
                    报告生成时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
                </small>
            </div>
        </div>
    </div>
    '''

    return report

def perform_single_analysis(tool_id, analysis_type, filepath):
    """执行单个文件分析"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        if tool_id == 'c_analysis':
            return perform_c_analysis(analysis_type, content)
        else:
            return perform_java_analysis(analysis_type, content)

    except Exception as e:
        return {
            'text': f'分析失败: {str(e)}',
            'chart': None
        }

def perform_c_analysis(analysis_type, content):
    """执行C工具分析"""
    if analysis_type == 'cpu':
        data = parse_cpu_data(content)
        if data:
            stats = analyze_cpu_usage(data)
            return {
                'text': f"CPU分析结果:\n平均使用率: {stats['stats']['平均CPU使用率']}\n最大使用率: {stats['stats']['最大CPU使用率']}\n数据点数: {len(data)}",
                'chart': stats['chart']
            }
        else:
            return {'text': '未找到有效的CPU数据', 'chart': None}

    elif analysis_type == 'memory':
        data = parse_memory_data(content)
        if data:
            stats = analyze_memory_usage(data)
            return {
                'text': f"内存分析结果:\n平均使用量: {stats['stats']['平均内存使用']}\n最大使用量: {stats['stats']['最大内存使用']}\n数据点数: {len(data)}",
                'chart': stats['chart']
            }
        else:
            return {'text': '未找到有效的内存数据', 'chart': None}

    elif analysis_type == 'performance':
        data = parse_performance_data(content)
        if data:
            stats = analyze_performance_data(data)
            return {
                'text': f"性能分析结果:\n平均检测时间: {stats['stats']['平均检测时间']}\n检测次数: {stats['stats']['检测次数']}",
                'chart': None
            }
        else:
            return {'text': '未找到有效的性能数据', 'chart': None}

    return {'text': '未知的分析类型', 'chart': None}

def perform_java_analysis(analysis_type, content):
    """执行Java工具分析"""
    if analysis_type == 'javaCpu':
        data = parse_java_cpu_memory_data(content)
        if data:
            stats = analyze_java_cpu_memory(data)
            return {
                'text': f"CPU分析结果:\n平均CPU使用率: {stats['stats']['平均CPU使用率']}\n平均内存使用: {stats['stats']['平均内存使用']}\n数据点数: {len(data)}",
                'chart': stats['chart']
            }
        else:
            return {'text': '未找到有效的CPU数据', 'chart': None}

    elif analysis_type == 'javaMemory':
        data = parse_java_cpu_memory_data(content)
        if data:
            stats = analyze_java_cpu_memory(data)
            return {
                'text': f"内存分析结果:\n平均内存使用: {stats['stats']['平均内存使用']}\n数据点数: {len(data)}",
                'chart': stats['chart']
            }
        else:
            return {'text': '未找到有效的内存数据', 'chart': None}

    elif analysis_type == 'javaPerformance':
        data = parse_java_performance_data(content)
        if data:
            stats = analyze_java_performance(data)
            return {
                'text': f"性能分析结果:\n平均执行时间: {stats['stats']['平均执行时间']}\n模块总数: {stats['stats']['模块总数']}",
                'chart': stats['chart']
            }
        else:
            return {'text': '未找到有效的性能数据', 'chart': None}

    elif analysis_type == 'javaEncryption':
        data = parse_java_encryption_data(content)
        if data:
            stats = analyze_java_encryption(data)
            return {
                'text': f"加密分析结果:\n检测算法数: {stats['stats']['检测算法数']}\n检测总次数: {stats['stats']['检测总次数']}",
                'chart': None
            }
        else:
            return {'text': '未找到有效的加密数据', 'chart': None}

    return {'text': '未知的分析类型', 'chart': None}

def perform_batch_analysis_logic(tool_id, batch_files):
    """执行批量分析逻辑"""
    results = []

    for file_info in batch_files:
        filepath = file_info['filepath']
        filename = file_info['filename']

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 根据文件名判断类型
            if 'CPU' in filename or 'cpu' in filename:
                if tool_id == 'c_analysis':
                    result = perform_c_analysis('cpu', content)
                else:
                    result = perform_java_analysis('javaCpu', content)
            elif 'Memory' in filename or 'memory' in filename:
                if tool_id == 'c_analysis':
                    result = perform_c_analysis('memory', content)
                else:
                    result = perform_java_analysis('javaMemory', content)
            elif 'performance' in filename.lower():
                if tool_id == 'c_analysis':
                    result = perform_c_analysis('performance', content)
                else:
                    result = perform_java_analysis('javaPerformance', content)
            else:
                result = {'text': f'无法识别文件类型: {filename}', 'chart': None}

            results.append({
                'filename': filename,
                'result': result
            })

        except Exception as e:
            results.append({
                'filename': filename,
                'result': {'text': f'分析失败: {str(e)}', 'chart': None}
            })

    return results

if __name__ == '__main__':
    # 注册信号处理器和退出清理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup_all_processes)

    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print("支持多用户登录:")
    for username in USERS.keys():
        print(f"  用户: {username} / 密码: {USERS[username]}")
    print("=" * 60)
    print("功能特性:")
    print("  ✓ 网页版分析工具（C和Java）")
    print("  ✓ 客户端下载EXE程序")
    print("  ✓ 服务器端启动工具")
    print("  ✓ 自动清理进程（关闭服务器时）")
    print("  ✓ 多用户并发支持")
    print("=" * 60)

    # 检查EXE程序是否存在
    exe_programs = {
        'C标准分析工具.exe': '../exe_programs/C标准分析工具.exe',
        'Java标准化分析工具.exe': '../exe_programs/Java标准化分析工具.exe',
        '商用车标准化测试统计工具v2.4.exe': '../exe_programs/商用车标准化测试统计工具v2.4.exe'
    }

    print("检查EXE程序...")
    for name, path in exe_programs.items():
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, path))
        if os.path.exists(abs_path):
            size_mb = os.path.getsize(abs_path) / (1024 * 1024)
            print(f"✓ {name}: {abs_path} ({size_mb:.1f} MB)")
        else:
            print(f"✗ {name}: {abs_path} (不存在)")

    print("=" * 60)

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        cleanup_all_processes()
    except Exception as e:
        print(f"服务器错误: {e}")
        cleanup_all_processes()
