#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证应用启动和脚本路径
"""

import os
import sys

def main():
    print("=" * 60)
    print("分析工具平台 - 启动验证")
    print("=" * 60)
    
    # 检查Flask是否可用
    try:
        import flask
        print(f"✓ Flask已安装，版本: {flask.__version__}")
    except ImportError:
        print("✗ Flask未安装，请运行: pip install flask")
        return False
    
    # 检查应用文件
    if os.path.exists('app.py'):
        print("✓ app.py 存在")
    else:
        print("✗ app.py 不存在")
        return False
    
    # 检查模板文件
    templates = ['templates/login.html', 'templates/dashboard.html', 'templates/base.html']
    for template in templates:
        if os.path.exists(template):
            print(f"✓ {template} 存在")
        else:
            print(f"✗ {template} 不存在")
            return False
    
    # 检查脚本文件
    scripts = {
        'C标准分析工具UI.py': '../C标准分析工具UI.py',
        'Java标准化分析工具UI.py': '../Java标准化分析工具UI.py'
    }
    
    print("\n检查原始脚本文件:")
    for name, path in scripts.items():
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, path))
        if os.path.exists(abs_path):
            print(f"✓ {name}: {abs_path}")
        else:
            print(f"✗ {name}: {abs_path} (不存在)")
    
    # 尝试导入应用
    try:
        sys.path.insert(0, '.')
        from app import app
        print("✓ Flask应用导入成功")
        
        # 检查路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.rule} -> {rule.endpoint}")
        
        print(f"✓ 发现 {len(routes)} 个路由")
        
    except Exception as e:
        print(f"✗ 应用导入失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 验证完成！应用可以启动")
    print("\n启动命令:")
    print("  python app.py")
    print("  或")
    print("  python run.py")
    print("\n访问地址: http://localhost:5000")
    print("登录信息: lcj / 123")
    print("=" * 60)
    
    return True

if __name__ == '__main__':
    main()
