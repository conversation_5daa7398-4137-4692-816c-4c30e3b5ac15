import os
import sys

print("检查项目结构...")

files_to_check = [
    'app.py',
    'config.py', 
    'auth.py',
    'run.py',
    'requirements.txt',
    'tools/__init__.py',
    'tools/base_tool.py',
    'tools/c_analysis.py',
    'tools/java_analysis.py',
    'templates/base.html',
    'templates/login.html',
    'templates/dashboard.html',
    'templates/tool_interface.html'
]

missing = []
for file in files_to_check:
    if os.path.exists(file):
        print(f"✓ {file}")
    else:
        print(f"✗ {file}")
        missing.append(file)

if missing:
    print(f"\n缺少 {len(missing)} 个文件")
else:
    print(f"\n所有 {len(files_to_check)} 个文件都存在")
    print("项目结构完整！")

print("\n尝试导入应用...")
try:
    sys.path.insert(0, '.')
    import app
    print("✓ 应用导入成功")
except Exception as e:
    print(f"✗ 应用导入失败: {e}")
