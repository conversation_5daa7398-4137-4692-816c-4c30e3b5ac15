#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页版分析工具功能测试
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_web_tool_functionality():
    """测试网页版工具功能"""
    print("=" * 60)
    print("网页版分析工具功能测试")
    print("=" * 60)
    
    # 测试应用导入
    try:
        from app import app
        print("✓ Flask应用导入成功")
    except Exception as e:
        print(f"✗ Flask应用导入失败: {e}")
        return False
    
    # 测试新增路由
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append(rule.rule)
    
    expected_routes = [
        '/web_tool/<tool_id>',
        '/api/upload_files',
        '/api/analyze_files'
    ]
    
    print("\n检查新增路由:")
    for route in expected_routes:
        # 检查路由模式是否存在
        route_exists = any(route.replace('<tool_id>', 'test') in r or route in r for r in routes)
        if route_exists:
            print(f"✓ {route}")
        else:
            print(f"✗ {route} 未找到")
    
    # 测试模板文件
    print("\n检查模板文件:")
    template_files = [
        'templates/web_tool.html',
        'templates/tool_interface.html'
    ]
    
    for template in template_files:
        if os.path.exists(template):
            print(f"✓ {template}")
        else:
            print(f"✗ {template}")
    
    # 测试上传目录
    print("\n检查上传目录:")
    upload_dir = 'static/uploads'
    if os.path.exists(upload_dir):
        print(f"✓ {upload_dir}")
    else:
        print(f"✗ {upload_dir}")
    
    # 测试分析函数
    print("\n检查分析函数:")
    try:
        from app import analyze_c_files, analyze_java_files
        print("✓ C分析函数导入成功")
        print("✓ Java分析函数导入成功")
    except Exception as e:
        print(f"✗ 分析函数导入失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 网页版工具功能测试完成")
    print("=" * 60)
    
    return True

def test_analysis_functions():
    """测试分析函数"""
    print("\n测试分析函数:")
    
    try:
        from app import parse_cpu_data, parse_memory_data, analyze_cpu_usage
        
        # 测试CPU数据解析
        test_cpu_content = """
# CPU数据测试
2024-01-01_10:00:00 45.2
2024-01-01_10:00:01 47.8
2024-01-01_10:00:02 52.1
        """
        
        cpu_data = parse_cpu_data(test_cpu_content)
        if len(cpu_data) == 3:
            print("✓ CPU数据解析功能正常")
        else:
            print("✗ CPU数据解析功能异常")
        
        # 测试CPU分析
        if cpu_data:
            cpu_analysis = analyze_cpu_usage(cpu_data)
            if 'stats' in cpu_analysis and 'chart' in cpu_analysis:
                print("✓ CPU分析功能正常")
            else:
                print("✗ CPU分析功能异常")
        
    except Exception as e:
        print(f"✗ 分析函数测试失败: {e}")

def create_test_data_files():
    """创建测试数据文件"""
    print("\n创建测试数据文件:")
    
    test_data_dir = 'static/test_data'
    os.makedirs(test_data_dir, exist_ok=True)
    
    # C工具测试数据
    cpu_test_data = """# CPU使用率测试数据
2024-01-01_10:00:00 45.2
2024-01-01_10:00:01 47.8
2024-01-01_10:00:02 52.1
2024-01-01_10:00:03 48.5
2024-01-01_10:00:04 51.3
"""
    
    memory_test_data = """# 内存使用量测试数据
2024-01-01_10:00:00 1024.5
2024-01-01_10:00:01 1056.2
2024-01-01_10:00:02 1089.7
2024-01-01_10:00:03 1045.8
2024-01-01_10:00:04 1078.3
"""
    
    performance_test_data = """# 性能检测测试数据
2024-01-01_10:00:00 15.6
2024-01-01_10:00:01 18.2
2024-01-01_10:00:02 16.8
2024-01-01_10:00:03 17.5
2024-01-01_10:00:04 19.1
"""
    
    # Java工具测试数据
    java_cpu_memory_data = """# Java CPU/内存测试数据
2024-01-01_10:00:00 45.2 1024.5
2024-01-01_10:00:01 47.8 1056.2
2024-01-01_10:00:02 52.1 1089.7
2024-01-01_10:00:03 48.5 1045.8
2024-01-01_10:00:04 51.3 1078.3
"""
    
    java_performance_data = """# Java性能模块测试数据
ModuleA 125.6 success
ModuleB 89.3 success
ModuleC 156.8 success
ModuleA 132.1 success
ModuleB 91.7 success
"""
    
    java_encryption_data = """# Java加密检测测试数据
2024-01-01_10:00:00 AES success
2024-01-01_10:00:01 RSA success
2024-01-01_10:00:02 DES failed
2024-01-01_10:00:03 AES success
2024-01-01_10:00:04 RSA success
"""
    
    # 写入测试文件
    test_files = {
        'stability_CPU_test.txt': cpu_test_data,
        'stability_Memory_test.txt': memory_test_data,
        'stability_Detect_test.txt': performance_test_data,
        'java_cpu_memory_test.txt': java_cpu_memory_data,
        'java_performance_test.txt': java_performance_data,
        'java_encryption_test.txt': java_encryption_data
    }
    
    for filename, content in test_files.items():
        filepath = os.path.join(test_data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 创建测试文件: {filename}")
    
    print(f"\n测试数据文件已创建在: {test_data_dir}")
    print("您可以使用这些文件测试网页版分析功能")

def main():
    """主函数"""
    success = test_web_tool_functionality()
    test_analysis_functions()
    create_test_data_files()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 网页版分析工具功能集成成功！")
        print("\n现在可以启动应用测试网页版功能:")
        print("1. 运行: python app.py")
        print("2. 访问: http://localhost:5000")
        print("3. 登录后选择工具")
        print("4. 点击'网页版启动（推荐）'")
        print("5. 上传测试数据文件进行分析")
        print("\n测试数据文件位置: static/test_data/")
    else:
        print("❌ 网页版分析工具功能集成存在问题，请检查配置")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
