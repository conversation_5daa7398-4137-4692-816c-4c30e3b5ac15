#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Flask基本功能
"""

try:
    print("测试Flask导入...")
    from flask import Flask
    print("✓ Flask导入成功")
    
    print("创建Flask应用...")
    app = Flask(__name__)
    app.secret_key = 'test'
    print("✓ Flask应用创建成功")
    
    @app.route('/')
    def hello():
        return '<h1>Hello World!</h1><p>Flask应用运行正常</p>'
    
    print("启动测试服务器...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
