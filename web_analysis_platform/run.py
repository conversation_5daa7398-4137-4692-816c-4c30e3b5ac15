#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print(f"登录账号: lcj")
    print(f"登录密码: 123")
    print("=" * 60)
    
    # 启动Flask应用
    app.run(debug=True, host='0.0.0.0', port=5000)
