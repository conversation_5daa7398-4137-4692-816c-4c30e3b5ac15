#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版分析工具平台
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash
import os
import subprocess

app = Flask(__name__)
app.secret_key = 'analysis-platform-secret-key-2025'

# 用户认证配置
USERS = {'lcj': '123'}

def check_credentials(username, password):
    return username in USERS and USERS[username] == password

def require_login(f):
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# HTML模板
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 分析工具平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 10vh;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-lock text-primary" style="font-size: 3rem;"></i>
                            <h3 class="mt-3">用户登录</h3>
                            <p class="text-muted">请输入您的登录凭据</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>用户名
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-key me-2"></i>密码
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>登录
                            </button>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                账号: lcj, 密码: 123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - 分析工具平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        .tool-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .btn {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>分析工具平台
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.username }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>登出
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-tachometer-alt me-2"></i>分析工具仪表板
                        </h2>
                        <p class="card-text text-muted">
                            点击下方按钮直接启动对应的分析工具GUI
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="tool-icon text-primary">
                            <i class="fas fa-code"></i>
                        </div>
                        <h5 class="card-title">C标准化分析工具</h5>
                        <p class="card-text text-muted">点击直接启动C标准分析工具GUI</p>
                        <a href="{{ url_for('tool_interface', tool_id='c_analysis') }}" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>启动GUI工具
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="tool-icon text-warning">
                            <i class="fab fa-java"></i>
                        </div>
                        <h5 class="card-title">Java标准化分析工具</h5>
                        <p class="card-text text-muted">点击直接启动Java标准化分析工具GUI</p>
                        <a href="{{ url_for('tool_interface', tool_id='java_analysis') }}" class="btn btn-warning">
                            <i class="fas fa-external-link-alt me-2"></i>启动GUI工具
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

@app.route('/')
def index():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if check_credentials(username, password):
            session['logged_in'] = True
            session['username'] = username
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('已成功登出！', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@require_login
def dashboard():
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/tool/<tool_id>')
@require_login
def tool_interface(tool_id):
    script_paths = {
        'c_analysis': '../C标准分析工具UI.py',
        'java_analysis': '../Java标准化分析工具UI.py'
    }
    
    tool_names = {
        'c_analysis': 'C标准化分析工具',
        'java_analysis': 'Java标准化分析工具'
    }
    
    if tool_id not in script_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))
    
    script_path = script_paths[tool_id]
    tool_name = tool_names[tool_id]
    
    # 解析绝对路径
    if not os.path.isabs(script_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, script_path))
    else:
        abs_path = script_path
    
    print(f"尝试启动: {tool_name}")
    print(f"脚本路径: {abs_path}")
    
    if not os.path.exists(abs_path):
        flash(f'脚本文件不存在: {abs_path}', 'error')
        print(f"错误: 脚本文件不存在")
        return redirect(url_for('dashboard'))
    
    try:
        # 启动脚本（非阻塞方式）
        process = subprocess.Popen(['python', abs_path], 
                        cwd=os.path.dirname(abs_path),
                        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        flash(f'{tool_name} 已启动！进程ID: {process.pid}', 'success')
        print(f"成功启动，进程ID: {process.pid}")
        return redirect(url_for('dashboard'))
        
    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        print(f"启动失败: {str(e)}")
        return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print(f"登录账号: lcj")
    print(f"登录密码: 123")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
