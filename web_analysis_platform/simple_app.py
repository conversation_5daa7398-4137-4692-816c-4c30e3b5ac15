#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版应用用于测试
"""

from flask import Flask, render_template, redirect, url_for, session, flash
import os
import subprocess

app = Flask(__name__)
app.secret_key = 'test-secret-key'

@app.route('/')
def index():
    """首页"""
    return redirect(url_for('dashboard'))

@app.route('/dashboard')
def dashboard():
    """仪表板"""
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis', 
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        }
    ]
    return render_template('dashboard.html', tools=tools)

@app.route('/tool/<tool_id>')
def tool_interface(tool_id):
    """工具界面 - 直接启动原始脚本"""
    
    # 脚本路径映射
    script_paths = {
        'c_analysis': '../C标准分析工具UI.py',
        'java_analysis': '../Java标准化分析工具UI.py'
    }
    
    if tool_id not in script_paths:
        flash('工具不存在！', 'error')
        return redirect(url_for('dashboard'))
    
    script_path = script_paths[tool_id]
    
    # 解析绝对路径
    if not os.path.isabs(script_path):
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        abs_path = os.path.abspath(os.path.join(base_dir, script_path))
    else:
        abs_path = script_path
    
    if not os.path.exists(abs_path):
        flash(f'脚本文件不存在: {abs_path}', 'error')
        return redirect(url_for('dashboard'))
    
    try:
        # 启动脚本（非阻塞方式）
        subprocess.Popen(['python', abs_path], 
                        cwd=os.path.dirname(abs_path),
                        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        tool_names = {
            'c_analysis': 'C标准化分析工具',
            'java_analysis': 'Java标准化分析工具'
        }
        
        flash(f'{tool_names.get(tool_id, "工具")} 已启动！', 'success')
        return redirect(url_for('dashboard'))
        
    except Exception as e:
        flash(f'启动工具失败: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

if __name__ == '__main__':
    print("=" * 60)
    print("分析工具平台启动中...")
    print("=" * 60)
    print(f"访问地址: http://localhost:5000")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
