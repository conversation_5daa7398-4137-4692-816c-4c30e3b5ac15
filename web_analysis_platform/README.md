# 分析工具平台

一个集成多个分析工具的Web平台，支持C标准化分析工具和Java标准化分析工具。

## 功能特性

- 🔐 **安全登录**: 基于Session的用户认证系统
- 🛠️ **工具启动**: 点击按钮直接启动原始GUI工具
- 🖥️ **原生体验**: 保持原始工具的所有功能和界面
- 🚀 **快速启动**: 一键启动，无需手动查找脚本文件
- 🎨 **美观界面**: 基于Bootstrap的现代化Web管理界面
- 🔧 **易扩展**: 模块化设计，便于添加新的分析工具
- 💻 **多实例**: 支持同时启动多个工具实例

## 系统要求

- Python 3.7+
- Flask 2.3+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

## 安装和运行

### 1. 安装依赖

```bash
cd web_analysis_platform
pip install -r requirements.txt
```

### 2. 启动应用

```bash
python run.py
```

或者直接运行：

```bash
python app.py
```

### 3. 访问应用

打开浏览器访问：http://localhost:5000

**登录信息：**
- 用户名：`lcj`
- 密码：`123`

## 项目结构

```
web_analysis_platform/
├── app.py                 # Flask主应用
├── run.py                 # 启动脚本
├── config.py             # 配置文件
├── auth.py               # 认证模块
├── requirements.txt      # 依赖列表
├── tools/                # 工具管理模块
│   ├── __init__.py
│   ├── base_tool.py      # 工具基类
│   ├── c_analysis.py     # C分析工具包装器
│   └── java_analysis.py  # Java分析工具包装器
├── templates/            # HTML模板
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   └── tool_interface.html
└── static/               # 静态资源
    ├── css/
    ├── js/
    ├── uploads/          # 文件上传目录
    └── results/          # 分析结果目录
```

## 已集成工具

### 1. C标准化分析工具
- **启动方式**: 点击"启动GUI工具"按钮
- **功能**: 完整的原始C标准分析工具GUI
- **特点**: 保持所有原有功能，包括文件选择、分析、图表显示等

### 2. Java标准化分析工具
- **启动方式**: 点击"启动GUI工具"按钮
- **功能**: 完整的原始Java标准化分析工具GUI
- **特点**: 保持所有原有功能，包括批量分析、结果展示等

## 添加新工具

### 1. 创建工具类

在 `tools/` 目录下创建新的工具类，继承自 `BaseTool`：

```python
from .base_tool import BaseTool

class NewAnalysisTool(BaseTool):
    def run_analysis(self, input_dir, output_dir, params=None):
        # 实现分析逻辑
        pass
```

### 2. 配置工具信息

在 `config.py` 中的 `TOOLS_CONFIG` 添加新工具配置：

```python
'new_tool': {
    'name': '新分析工具',
    'description': '工具描述',
    'script_path': '../../your_script.py',
    'icon': 'fas fa-cog',
    'color': 'info',
    'file_types': [
        {
            'key': 'input_file',
            'label': '输入文件',
            'accept': '.txt',
            'required': True
        }
    ]
}
```

### 3. 注册工具

在 `tools/__init__.py` 中注册新工具：

```python
from .new_tool import NewAnalysisTool

def _register_tools(self):
    # ... 现有工具注册代码 ...
    
    # 注册新工具
    if 'new_tool' in Config.TOOLS_CONFIG:
        self.tools['new_tool'] = NewAnalysisTool('new_tool', Config.TOOLS_CONFIG['new_tool'])
```

## API接口

### 文件上传
- **URL**: `/api/upload`
- **方法**: POST
- **参数**: multipart/form-data

### 启动分析
- **URL**: `/api/analyze`
- **方法**: POST
- **参数**: JSON格式

### 查询状态
- **URL**: `/api/status/<task_id>`
- **方法**: GET

### 下载结果
- **URL**: `/api/download/<task_id>/<filename>`
- **方法**: GET

## 注意事项

1. **原始脚本**: 本平台不会修改您的原始分析脚本，只是通过包装器调用它们
2. **文件安全**: 上传的文件会存储在临时目录中，分析完成后可以手动清理
3. **并发处理**: 支持多个分析任务同时进行
4. **错误处理**: 完善的错误处理和用户反馈机制

## 技术栈

- **后端**: Flask (Python)
- **前端**: Bootstrap 5 + jQuery
- **图标**: Font Awesome
- **认证**: Session-based
- **文件处理**: Werkzeug

## 许可证

本项目仅供内部使用。
