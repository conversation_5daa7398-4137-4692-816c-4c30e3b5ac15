{% extends "base.html" %}

{% block title %}{{ tool_name }} - 分析工具平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title mb-1">
                            <i class="fas fa-{{ 'code' if tool_id == 'c_analysis' else ('java' if tool_id == 'java_analysis' else 'truck') }} me-2"></i>{{ tool_name }}
                        </h2>
                        <p class="card-text text-muted mb-0">选择启动方式</p>
                    </div>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 客户端启动（推荐） -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>客户端启动（推荐）
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-desktop fa-3x text-success mb-3"></i>
                    <h6>在您的电脑上运行</h6>
                </div>
                
                <div class="mb-3">
                    <h6>优势：</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>在您的电脑上运行</li>
                        <li><i class="fas fa-check text-success me-2"></i>不占用服务器资源</li>
                        <li><i class="fas fa-check text-success me-2"></i>运行更稳定</li>
                        <li><i class="fas fa-check text-success me-2"></i>支持离线使用</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>使用步骤：</h6>
                    <ol class="small">
                        <li>点击下载按钮下载EXE程序</li>
                        <li>保存到您的电脑任意位置</li>
                        <li>双击EXE文件即可运行</li>
                        <li>首次运行可能需要几秒钟加载</li>
                    </ol>
                </div>
                
                <div class="text-center">
                    <a href="{{ url_for('download_tool', tool_id=tool_id) }}" 
                       class="btn btn-success btn-lg">
                        <i class="fas fa-download me-2"></i>下载到本地
                    </a>
                    <div class="mt-2">
                        <small class="text-muted">文件大小约 50-100MB</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 服务器端启动 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>服务器端启动
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <i class="fas fa-server fa-3x text-warning mb-3"></i>
                    <h6>在服务器上运行</h6>
                </div>
                
                <div class="mb-3">
                    <h6>特点：</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-info text-warning me-2"></i>在服务器电脑上运行</li>
                        <li><i class="fas fa-info text-warning me-2"></i>占用服务器资源</li>
                        <li><i class="fas fa-info text-warning me-2"></i>需要服务器保持运行</li>
                        <li><i class="fas fa-info text-warning me-2"></i>适合临时使用</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6>使用说明：</h6>
                    <ol class="small">
                        <li>点击启动按钮</li>
                        <li>工具将在服务器电脑上打开</li>
                        <li>您需要到服务器电脑前操作</li>
                        <li>关闭服务器时程序会自动结束</li>
                    </ol>
                </div>
                
                <div class="text-center">
                    <a href="{{ url_for('launch_local', tool_id=tool_id) }}" 
                       class="btn btn-warning btn-lg">
                        <i class="fas fa-play me-2"></i>服务器端启动
                    </a>
                    <div class="mt-2">
                        <small class="text-muted">在服务器电脑上运行</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb text-info me-2"></i>推荐方式</h6>
                        <p class="small text-muted">
                            建议使用<strong>客户端启动</strong>方式，这样工具运行在您自己的电脑上，
                            不会占用服务器资源，运行更稳定，也支持离线使用。
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-success me-2"></i>安全说明</h6>
                        <p class="small text-muted">
                            下载的EXE程序是从您的原始Python脚本打包生成的，
                            完全安全可靠，包含了所有必要的依赖库。
                        </p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6><i class="fas fa-cog text-primary me-2"></i>系统要求</h6>
                        <ul class="small text-muted">
                            <li>Windows 7/8/10/11</li>
                            <li>至少 200MB 可用磁盘空间</li>
                            <li>无需安装Python环境</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-question text-warning me-2"></i>常见问题</h6>
                        <ul class="small text-muted">
                            <li>首次运行可能需要几秒钟加载</li>
                            <li>如果被杀毒软件拦截，请添加信任</li>
                            <li>可以复制到其他电脑使用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 下载进度提示
document.querySelector('a[href*="download_tool"]').addEventListener('click', function(e) {
    // 显示下载提示
    const button = this;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>准备下载...';
    button.classList.add('disabled');
    
    // 3秒后恢复按钮状态
    setTimeout(function() {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    }, 3000);
});

// 服务器启动确认
document.querySelector('a[href*="launch_local"]').addEventListener('click', function(e) {
    if (!confirm('确定要在服务器端启动工具吗？\n\n注意：工具将在服务器电脑上运行，您需要到服务器前操作。')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
