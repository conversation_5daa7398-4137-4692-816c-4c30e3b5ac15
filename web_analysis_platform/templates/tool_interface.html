{% extends "base.html" %}

{% block title %}{{ tool.name }} - 分析工具平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title mb-1">
                            <i class="{{ tool.icon }} me-2"></i>{{ tool.name }}
                        </h2>
                        <p class="card-text text-muted mb-0">{{ tool.description }}</p>
                    </div>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 文件上传区域 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>文件上传
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="tool_id" value="{{ tool.id }}">
                    
                    {% for file_type in tool.file_types %}
                    <div class="mb-3">
                        <label for="{{ file_type.key }}" class="form-label">
                            {{ file_type.label }}
                            {% if file_type.required %}
                            <span class="text-danger">*</span>
                            {% endif %}
                        </label>
                        
                        {% if file_type.type == 'directory' %}
                        <input type="text" class="form-control" name="{{ file_type.key }}" 
                               placeholder="请输入目录路径">
                        {% elif file_type.type == 'text' %}
                        <input type="text" class="form-control" name="{{ file_type.key }}" 
                               placeholder="请输入{{ file_type.label }}">
                        {% else %}
                        <input type="file" class="form-control" name="{{ file_type.key }}" 
                               accept="{{ file_type.accept or '' }}">
                        {% endif %}
                    </div>
                    {% endfor %}
                    
                    <!-- 拖拽上传区域 -->
                    <div class="file-upload-area" id="dropZone">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>拖拽文件到此处</h5>
                        <p class="text-muted">或点击选择文件</p>
                        <input type="file" id="fileInput" multiple style="display: none;">
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload me-2"></i>上传并分析
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" id="clearBtn">
                            <i class="fas fa-trash me-2"></i>清除
                        </button>
                    </div>
                </form>
                
                <!-- 上传进度 -->
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted mt-1 d-block">正在上传文件...</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分析状态和结果 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>分析结果
                </h5>
            </div>
            <div class="card-body">
                <div id="analysisStatus" class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                    <p class="text-muted">请先上传文件开始分析</p>
                </div>
                
                <div id="analysisResult" style="display: none;">
                    <!-- 分析结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细结果展示区域 -->
<div class="row mt-4" id="detailedResults" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>详细分析结果
                </h5>
            </div>
            <div class="card-body">
                <div id="detailedContent">
                    <!-- 详细结果内容 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTaskId = null;
let statusCheckInterval = null;

$(document).ready(function() {
    // 文件拖拽功能
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    
    dropZone.addEventListener('click', () => fileInput.click());
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            // 将文件添加到第一个文件输入框
            const firstFileInput = document.querySelector('input[type="file"]');
            if (firstFileInput) {
                firstFileInput.files = files;
            }
        }
    });
    
    // 表单提交
    $('#uploadForm').on('submit', function(e) {
        e.preventDefault();
        uploadAndAnalyze();
    });
    
    // 清除按钮
    $('#clearBtn').on('click', function() {
        $('#uploadForm')[0].reset();
        $('#analysisStatus').html(`
            <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
            <p class="text-muted">请先上传文件开始分析</p>
        `).show();
        $('#analysisResult').hide();
        $('#detailedResults').hide();
        
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
    });
});

function uploadAndAnalyze() {
    const formData = new FormData($('#uploadForm')[0]);
    
    // 显示上传进度
    $('#uploadProgress').show();
    $('#uploadBtn').prop('disabled', true);
    
    // 上传文件
    $.ajax({
        url: '/api/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#uploadProgress').hide();
            
            if (response.success) {
                currentTaskId = response.task_id;
                startAnalysis(response.task_id);
            } else {
                showError(response.message);
                $('#uploadBtn').prop('disabled', false);
            }
        },
        error: function() {
            $('#uploadProgress').hide();
            showError('文件上传失败');
            $('#uploadBtn').prop('disabled', false);
        }
    });
}

function startAnalysis(taskId) {
    // 显示分析状态
    $('#analysisStatus').html(`
        <div class="spinner-border text-primary mb-3" role="status"></div>
        <p class="text-primary">正在启动分析任务...</p>
    `);
    
    // 启动分析
    $.ajax({
        url: '/api/analyze',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            tool_id: '{{ tool.id }}',
            task_id: taskId
        }),
        success: function(response) {
            if (response.success) {
                // 开始检查状态
                checkAnalysisStatus(taskId);
            } else {
                showError(response.message);
                $('#uploadBtn').prop('disabled', false);
            }
        },
        error: function() {
            showError('启动分析失败');
            $('#uploadBtn').prop('disabled', false);
        }
    });
}

function checkAnalysisStatus(taskId) {
    $('#analysisStatus').html(`
        <div class="spinner-border text-warning mb-3" role="status"></div>
        <p class="text-warning">分析正在进行中，请稍候...</p>
    `);
    
    statusCheckInterval = setInterval(function() {
        $.ajax({
            url: `/api/status/${taskId}`,
            type: 'GET',
            success: function(response) {
                if (response.status === 'completed') {
                    clearInterval(statusCheckInterval);
                    showAnalysisResult(response.result);
                    $('#uploadBtn').prop('disabled', false);
                } else if (response.status === 'error') {
                    clearInterval(statusCheckInterval);
                    showError(response.message);
                    $('#uploadBtn').prop('disabled', false);
                }
                // 如果是 running 状态，继续等待
            },
            error: function() {
                clearInterval(statusCheckInterval);
                showError('获取分析状态失败');
                $('#uploadBtn').prop('disabled', false);
            }
        });
    }, 2000); // 每2秒检查一次
}

function showAnalysisResult(result) {
    $('#analysisStatus').html(`
        <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
        <p class="text-success">分析完成！</p>
    `);
    
    // 显示基本结果信息
    let resultHtml = `
        <div class="alert alert-success">
            <h6><i class="fas fa-info-circle me-2"></i>分析摘要</h6>
            <p class="mb-1"><strong>工具:</strong> ${result.tool_name}</p>
            <p class="mb-1"><strong>状态:</strong> ${result.success ? '成功' : '失败'}</p>
            <p class="mb-0"><strong>消息:</strong> ${result.message}</p>
        </div>
    `;
    
    if (result.files && result.files.length > 0) {
        resultHtml += `
            <div class="mt-3">
                <h6>生成的文件:</h6>
                <ul class="list-group">
        `;
        
        result.files.forEach(file => {
            resultHtml += `
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    ${file}
                    <a href="/api/download/${currentTaskId}/${file}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download"></i>
                    </a>
                </li>
            `;
        });
        
        resultHtml += `
                </ul>
            </div>
        `;
    }
    
    $('#analysisResult').html(resultHtml).show();
    
    // 显示详细结果
    if (result.data) {
        showDetailedResults(result.data);
    }
}

function showDetailedResults(data) {
    let detailedHtml = '<pre class="bg-light p-3 rounded">' + 
                      JSON.stringify(data, null, 2) + '</pre>';
    
    $('#detailedContent').html(detailedHtml);
    $('#detailedResults').show();
}

function showError(message) {
    $('#analysisStatus').html(`
        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
        <p class="text-danger">${message}</p>
    `);
}
</script>
{% endblock %}
