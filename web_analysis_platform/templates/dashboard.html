{% extends "base.html" %}

{% block title %}仪表板 - 分析工具平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-tachometer-alt me-2"></i>分析工具仪表板
                </h2>
                <p class="card-text text-muted">
                    欢迎 {{ session.username }} 使用分析工具平台！请选择您需要使用的分析工具。
                </p>
                {% if session_info %}
                <div class="row text-center">
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            登录时间: {{ session_info.login_time }}
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-users me-1"></i>
                            在线用户: {{ session_info.active_users }}人
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            当前用户: {{ session_info.username }}
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    {% for tool in tools %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="tool-icon text-{{ tool.color }}">
                    <i class="{{ tool.icon }}"></i>
                </div>
                <h5 class="card-title">{{ tool.name }}</h5>
                <p class="card-text text-muted">{{ tool.description }}</p>
                
                <div class="mt-auto">
                    <a href="{{ url_for('tool_interface', tool_id=tool.id) }}"
                       class="btn btn-{{ tool.color }}">
                        <i class="fas fa-cog me-2"></i>选择启动方式
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not tools %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-tools text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">暂无可用工具</h4>
                <p class="text-muted">请联系管理员添加分析工具。</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 系统信息卡片 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>系统信息
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-tools me-1"></i>
                            可用工具: {{ tools|length }}
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            当前用户: {{ session.username }}
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            登录时间: <span id="current-time"></span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 显示当前时间
function updateTime() {
    const now = new Date();
    document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
}

updateTime();
setInterval(updateTime, 1000);
</script>
{% endblock %}
