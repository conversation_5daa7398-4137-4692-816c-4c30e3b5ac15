{% extends "base.html" %}

{% block title %}Java标准化分析工具 - 网页版{% endblock %}

{% block extra_css %}
<style>
/* 完全模拟tkinter界面样式 */
.tkinter-frame {
    border: 1px solid #d0d0d0;
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
}

.tkinter-labelframe {
    border: 2px groove #d0d0d0;
    background: #f0f0f0;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

.tkinter-labelframe-title {
    position: absolute;
    top: -10px;
    left: 10px;
    background: #f0f0f0;
    padding: 0 5px;
    font-weight: bold;
    font-size: 12px;
}

.tkinter-button {
    background: #e1e1e1;
    border: 1px outset #d0d0d0;
    padding: 4px 8px;
    margin: 2px;
    font-size: 11px;
    cursor: pointer;
    min-width: 80px;
}

.tkinter-button:hover {
    background: #e8e8e8;
}

.tkinter-button:active {
    border: 1px inset #d0d0d0;
}

.tkinter-entry {
    border: 1px inset #d0d0d0;
    padding: 2px;
    background: white;
    font-size: 11px;
    width: 100%;
}

.tkinter-text {
    border: 1px inset #d0d0d0;
    background: white;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    padding: 5px;
    white-space: pre-wrap;
    overflow-y: auto;
    height: 200px;
    resize: vertical;
}

.notebook-tabs {
    border-bottom: 1px solid #d0d0d0;
    background: #f0f0f0;
    margin-bottom: 0;
    display: flex;
}

.notebook-tab {
    padding: 6px 12px;
    background: #e1e1e1;
    border: 1px solid #d0d0d0;
    border-bottom: none;
    margin-right: 2px;
    cursor: pointer;
    font-size: 11px;
}

.notebook-tab.active {
    background: #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: -1px;
}

.notebook-content {
    border: 1px solid #d0d0d0;
    border-top: none;
    background: #f0f0f0;
    padding: 10px;
    min-height: 500px;
}

.chart-frame {
    border: 2px groove #d0d0d0;
    background: white;
    padding: 10px;
    margin: 10px 0;
    min-height: 300px;
}

.file-entry-frame {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.file-entry-frame input {
    flex: 1;
    margin-right: 5px;
}

.control-frame {
    margin: 10px 0;
}

.control-frame button {
    margin-right: 5px;
}

.batch-frame {
    background: #f0f0f0;
    border: 1px solid #d0d0d0;
    padding: 10px;
    margin-bottom: 10px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 标题栏 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>Java标准化分析工具 - 网页版</h4>
        <a href="{{ url_for('tool_interface', tool_id='java_analysis') }}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回
        </a>
    </div>

    <!-- 批量分析区域 -->
    <div class="batch-frame">
        <div class="file-entry-frame">
            <input type="text" class="tkinter-entry" id="batchPath" placeholder="输入批量分析路径">
            <button class="tkinter-button" onclick="browseDirectory()">浏览</button>
            <button class="tkinter-button" onclick="batchAnalyze()">批量分析</button>
        </div>
    </div>

    <!-- 标签页区域 -->
    <div class="notebook-tabs" id="notebookTabs">
        <div class="notebook-tab active" data-tab="cpu">CPU分析</div>
        <div class="notebook-tab" data-tab="memory">内存分析</div>
        <div class="notebook-tab" data-tab="performance">性能分析</div>
        <div class="notebook-tab" data-tab="encryption">模型加密分析</div>
    </div>

    <div class="notebook-content" id="notebookContent">
        <!-- 标签页内容将通过JavaScript动态加载 -->
    </div>
</div>

<!-- 文件上传表单 -->
<input type="file" id="fileInput" style="display: none;" accept=".txt,.json,.log">
<input type="file" id="directoryInput" style="display: none;" webkitdirectory multiple>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.2.1/dist/chartjs-plugin-annotation.min.js"></script>
<script>
// 全局变量
let currentTab = 'cpu';
let charts = {};
let uploadedFiles = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    loadTabContent(currentTab);
});

// 初始化标签页功能
function initializeTabs() {
    const tabs = document.querySelectorAll('.notebook-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
            // 切换内容
            currentTab = this.dataset.tab;
            loadTabContent(currentTab);
        });
    });
}

// 加载标签页内容
function loadTabContent(tabName) {
    const content = document.getElementById('notebookContent');
    
    if (tabName === 'cpu') {
        content.innerHTML = createCPUTabContent();
    } else if (tabName === 'memory') {
        content.innerHTML = createMemoryTabContent();
    } else if (tabName === 'performance') {
        content.innerHTML = createPerformanceTabContent();
    } else if (tabName === 'encryption') {
        content.innerHTML = createEncryptionTabContent();
    }
}

// 创建CPU标签页内容
function createCPUTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="cpuFilePath" placeholder="选择CPU数据文件">
                <button class="tkinter-button" onclick="browseFile('cpu')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeCPU()">分析CPU数据</button>
                <button class="tkinter-button" onclick="copyCPUChart()">复制图表</button>
                <button class="tkinter-button" onclick="clearCPUChart()">清除图表</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">CPU使用率图表</div>
            <div class="chart-frame" id="cpuChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    CPU分析图表将在这里显示
                </div>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="cpuResults" readonly>CPU分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 创建内存标签页内容
function createMemoryTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="memoryFilePath" placeholder="选择内存数据文件">
                <button class="tkinter-button" onclick="browseFile('memory')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeMemory()">分析内存数据</button>
                <button class="tkinter-button" onclick="copyMemoryChart()">复制图表</button>
                <button class="tkinter-button" onclick="clearMemoryChart()">清除图表</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">内存使用情况图表</div>
            <div class="chart-frame" id="memoryChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    内存分析图表将在这里显示
                </div>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="memoryResults" readonly>内存分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 创建性能标签页内容
function createPerformanceTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="performanceFilePath" placeholder="选择性能数据文件">
                <button class="tkinter-button" onclick="browseFile('performance')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzePerformance()">分析性能数据</button>
                <button class="tkinter-button" onclick="clearPerformanceResults()">清除结果</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="performanceResults" readonly>性能分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 创建加密分析标签页内容
function createEncryptionTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="encryptionFilePath" placeholder="选择加密日志文件">
                <button class="tkinter-button" onclick="browseFile('encryption')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeEncryption()">分析DMS加密</button>
                <button class="tkinter-button" onclick="clearEncryptionResults()">清除结果</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="encryptionResults" readonly>加密分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 文件浏览功能
function browseFile(type) {
    const input = document.getElementById('fileInput');
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const pathInput = document.getElementById(type + 'FilePath');
            if (pathInput) {
                pathInput.value = file.name;
                uploadSingleFile(file, type);
            }
        }
    };
    input.click();
}

// 目录浏览功能
function browseDirectory() {
    const input = document.getElementById('directoryInput');
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            document.getElementById('batchPath').value = files[0].webkitRelativePath.split('/')[0];
            uploadMultipleFiles(files);
        }
    };
    input.click();
}

// 上传单个文件
function uploadSingleFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    fetch('/api/java_analysis/upload_file', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedFiles[type] = data.filepath;
            console.log('文件上传成功:', data.filepath);
        } else {
            alert('文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// 上传多个文件
function uploadMultipleFiles(files) {
    const formData = new FormData();
    files.forEach(file => {
        formData.append('files', file);
    });
    
    fetch('/api/java_analysis/upload_directory', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedFiles = data.file_paths;
            // 自动填充文件路径
            fillFilePathsFromBatch(data.file_paths);
            alert(data.feedback || '批量文件上传成功');
        } else {
            alert('批量文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// 从批量分析结果填充文件路径
function fillFilePathsFromBatch(filePaths) {
    if (filePaths.cpu_memory) {
        const cpuInput = document.getElementById('cpuFilePath');
        const memoryInput = document.getElementById('memoryFilePath');
        const filename = filePaths.cpu_memory.split('/').pop();
        if (cpuInput) cpuInput.value = filename;
        if (memoryInput) memoryInput.value = filename;
    }
    if (filePaths.performance) {
        const performanceInput = document.getElementById('performanceFilePath');
        if (performanceInput) performanceInput.value = filePaths.performance.split('/').pop();
    }
}

// 分析函数
function analyzeCPU() {
    if (!uploadedFiles.cpu && !uploadedFiles.cpu_memory) {
        alert('请先选择CPU数据文件');
        return;
    }
    const filePath = uploadedFiles.cpu || uploadedFiles.cpu_memory;
    performAnalysis('cpu', filePath, 'cpuResults', 'cpuChart');
}

function analyzeMemory() {
    if (!uploadedFiles.memory && !uploadedFiles.cpu_memory) {
        alert('请先选择内存数据文件');
        return;
    }
    const filePath = uploadedFiles.memory || uploadedFiles.cpu_memory;
    performAnalysis('memory', filePath, 'memoryResults', 'memoryChart');
}

function analyzePerformance() {
    if (!uploadedFiles.performance) {
        alert('请先选择性能数据文件');
        return;
    }
    performAnalysis('performance', uploadedFiles.performance, 'performanceResults');
}

function analyzeEncryption() {
    if (!uploadedFiles.encryption) {
        alert('请先选择加密日志文件');
        return;
    }
    performAnalysis('encryption', uploadedFiles.encryption, 'encryptionResults');
}

// 批量分析
function batchAnalyze() {
    if (Object.keys(uploadedFiles).length === 0) {
        alert('请先选择批量分析目录');
        return;
    }
    
    fetch('/api/java_analysis/batch_analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            file_paths: uploadedFiles
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示所有分析结果
            if (data.results.cpu) {
                displayAnalysisResult('cpu', data.results.cpu, 'cpuResults', 'cpuChart');
            }
            if (data.results.memory) {
                displayAnalysisResult('memory', data.results.memory, 'memoryResults', 'memoryChart');
            }
            if (data.results.performance) {
                displayAnalysisResult('performance', data.results.performance, 'performanceResults');
            }
            alert(data.feedback || '批量分析完成');
        } else {
            alert('批量分析失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('批量分析过程中发生错误: ' + error.message);
    });
}

// 执行分析
function performAnalysis(analysisType, filePath, resultsId, chartId) {
    const resultsElement = document.getElementById(resultsId);
    
    // 显示分析中状态
    if (resultsElement) {
        resultsElement.value = '正在分析中，请稍候...';
    }
    
    fetch('/api/java_analysis/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            analysis_type: analysisType,
            file_path: filePath
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResult(analysisType, data, resultsId, chartId);
        } else {
            if (resultsElement) {
                resultsElement.value = '分析失败: ' + data.message;
            }
        }
    })
    .catch(error => {
        if (resultsElement) {
            resultsElement.value = '分析过程中发生错误: ' + error.message;
        }
    });
}

// 显示分析结果
function displayAnalysisResult(analysisType, data, resultsId, chartId) {
    const resultsElement = document.getElementById(resultsId);
    
    // 显示结果文本
    if (resultsElement) {
        if (data.result_text) {
            resultsElement.value = data.result_text;
        } else if (data.output_text) {
            resultsElement.value = data.output_text;
        }
    }
    
    // 显示图表
    if (chartId && data.chart) {
        displayChart(chartId, data.chart);
    }
}

// 显示图表
function displayChart(chartId, chartData) {
    const chartContainer = document.getElementById(chartId);
    if (!chartContainer) return;
    
    chartContainer.innerHTML = '<canvas id="' + chartId + 'Canvas"></canvas>';
    
    const ctx = document.getElementById(chartId + 'Canvas').getContext('2d');
    if (charts[chartId]) {
        charts[chartId].destroy();
    }
    
    // 注册插件
    Chart.register(ChartjsPluginAnnotation);
    
    charts[chartId] = new Chart(ctx, chartData);
}

// 清除结果函数
function clearCPUChart() {
    clearChart('cpuChart', 'cpuResults', 'CPU分析图表将在这里显示', 'CPU分析结果将在这里显示...');
}

function clearMemoryChart() {
    clearChart('memoryChart', 'memoryResults', '内存分析图表将在这里显示', '内存分析结果将在这里显示...');
}

function clearPerformanceResults() {
    const resultsElement = document.getElementById('performanceResults');
    if (resultsElement) {
        resultsElement.value = '性能分析结果将在这里显示...';
    }
}

function clearEncryptionResults() {
    const resultsElement = document.getElementById('encryptionResults');
    if (resultsElement) {
        resultsElement.value = '加密分析结果将在这里显示...';
    }
}

function clearChart(chartId, resultsId, chartPlaceholder, resultsPlaceholder) {
    const chartElement = document.getElementById(chartId);
    const resultsElement = document.getElementById(resultsId);
    
    if (chartElement) {
        chartElement.innerHTML = `<div style="text-align: center; padding: 50px; color: #666;">${chartPlaceholder}</div>`;
    }
    if (resultsElement) {
        resultsElement.value = resultsPlaceholder;
    }
    
    if (charts[chartId]) {
        charts[chartId].destroy();
        delete charts[chartId];
    }
}

// 复制图表函数
function copyCPUChart() {
    copyChart('cpuChart');
}

function copyMemoryChart() {
    copyChart('memoryChart');
}

function copyChart(chartId) {
    if (charts[chartId]) {
        const canvas = document.getElementById(chartId + 'Canvas');
        canvas.toBlob(function(blob) {
            const item = new ClipboardItem({ "image/png": blob });
            navigator.clipboard.write([item]).then(function() {
                alert('图表已复制到剪贴板');
            }).catch(function(err) {
                alert('复制失败: ' + err);
            });
        });
    } else {
        alert('没有可复制的图表');
    }
}
</script>
{% endblock %}
