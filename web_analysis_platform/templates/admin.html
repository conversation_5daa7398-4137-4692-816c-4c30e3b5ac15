{% extends "base.html" %}

{% block title %}管理页面 - 分析工具平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-users-cog me-2"></i>用户管理
                </h2>
                <p class="card-text text-muted">
                    当前系统中的活跃用户和会话信息
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ total_users }}</h3>
                <p class="card-text">在线用户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ sessions|selectattr('processes_count', 'gt', 0)|list|length }}</h3>
                <p class="card-text">活跃会话</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ sessions|sum(attribute='processes_count') }}</h3>
                <p class="card-text">运行进程</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ sessions|length }}</h3>
                <p class="card-text">总会话数</p>
            </div>
        </div>
    </div>
</div>

<!-- 用户会话列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>活跃会话列表
                </h5>
            </div>
            <div class="card-body">
                {% if sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>会话ID</th>
                                <th>用户名</th>
                                <th>登录时间</th>
                                <th>最后活动</th>
                                <th>运行进程</th>
                                <th>进程详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in sessions %}
                            <tr>
                                <td>
                                    <code>{{ session.session_id }}</code>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ session.username }}</span>
                                </td>
                                <td>
                                    <small>{{ session.login_time }}</small>
                                </td>
                                <td>
                                    <small>{{ session.last_activity }}</small>
                                </td>
                                <td>
                                    {% if session.processes_count > 0 %}
                                    <span class="badge bg-success">{{ session.processes_count }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if session.processes %}
                                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#processes-{{ loop.index }}" aria-expanded="false">
                                        <i class="fas fa-eye me-1"></i>查看详情
                                    </button>
                                    {% else %}
                                    <span class="text-muted">无运行进程</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if session.processes %}
                            <tr>
                                <td colspan="6" class="p-0">
                                    <div class="collapse" id="processes-{{ loop.index }}">
                                        <div class="card card-body m-2">
                                            <h6>运行中的进程:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>工具名称</th>
                                                            <th>进程ID</th>
                                                            <th>启动时间</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for process in session.processes %}
                                                        <tr>
                                                            <td>{{ process.tool_name }}</td>
                                                            <td><code>{{ process.pid }}</code></td>
                                                            <td><small>{{ process.start_time.strftime('%Y-%m-%d %H:%M:%S') }}</small></td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无活跃用户</h5>
                    <p class="text-muted">当前没有用户在线</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 返回按钮 -->
<div class="row mt-4">
    <div class="col-12">
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>返回仪表板
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 自动刷新页面数据
setInterval(function() {
    // 每30秒刷新一次页面以更新用户状态
    location.reload();
}, 30000);

// 显示实时时间
function updateTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    document.title = `管理页面 (${timeStr}) - 分析工具平台`;
}

updateTime();
setInterval(updateTime, 1000);
</script>
{% endblock %}
