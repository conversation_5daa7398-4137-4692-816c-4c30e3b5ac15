{% extends "base.html" %}

{% block title %}C标准分析工具 - 网页版{% endblock %}

{% block extra_css %}
<style>
/* 完全模拟tkinter界面样式 */
.tkinter-frame {
    border: 1px solid #d0d0d0;
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
}

.tkinter-labelframe {
    border: 2px groove #d0d0d0;
    background: #f0f0f0;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

.tkinter-labelframe-title {
    position: absolute;
    top: -10px;
    left: 10px;
    background: #f0f0f0;
    padding: 0 5px;
    font-weight: bold;
    font-size: 12px;
}

.tkinter-button {
    background: #e1e1e1;
    border: 1px outset #d0d0d0;
    padding: 4px 8px;
    margin: 2px;
    font-size: 11px;
    cursor: pointer;
    min-width: 80px;
}

.tkinter-button:hover {
    background: #e8e8e8;
}

.tkinter-button:active {
    border: 1px inset #d0d0d0;
}

.tkinter-entry {
    border: 1px inset #d0d0d0;
    padding: 2px;
    background: white;
    font-size: 11px;
    width: 100%;
}

.tkinter-text {
    border: 1px inset #d0d0d0;
    background: white;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    padding: 5px;
    white-space: pre-wrap;
    overflow-y: auto;
    height: 150px;
    resize: vertical;
}

.notebook-tabs {
    border-bottom: 1px solid #d0d0d0;
    background: #f0f0f0;
    margin-bottom: 0;
    display: flex;
}

.notebook-tab {
    padding: 6px 12px;
    background: #e1e1e1;
    border: 1px solid #d0d0d0;
    border-bottom: none;
    margin-right: 2px;
    cursor: pointer;
    font-size: 11px;
}

.notebook-tab.active {
    background: #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: -1px;
}

.notebook-content {
    border: 1px solid #d0d0d0;
    border-top: none;
    background: #f0f0f0;
    padding: 10px;
    min-height: 500px;
}

.chart-frame {
    border: 2px groove #d0d0d0;
    background: white;
    padding: 10px;
    margin: 10px 0;
    min-height: 300px;
}

.file-entry-frame {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.file-entry-frame input {
    flex: 1;
    margin-right: 5px;
}

.control-frame {
    margin: 10px 0;
}

.control-frame button {
    margin-right: 5px;
}

.separator {
    border-top: 1px solid #d0d0d0;
    margin: 10px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 标题栏 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>C标准分析工具 - 网页版</h4>
        <a href="{{ url_for('tool_interface', tool_id='c_analysis') }}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回
        </a>
    </div>

    <!-- 统一目录选择区域 -->
    <div class="tkinter-labelframe">
        <div class="tkinter-labelframe-title">统一目录选择</div>
        <div class="mb-2">
            <label style="font-size: 11px;">选择结果目录:</label>
        </div>
        <div class="file-entry-frame">
            <input type="text" class="tkinter-entry" id="resultDirPath" placeholder="选择包含结果文件的目录">
            <button class="tkinter-button" onclick="browseDirectory()">浏览</button>
        </div>
        <div class="mt-2">
            <button class="tkinter-button" onclick="batchAnalyze()">批量分析</button>
        </div>
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 标签页区域 -->
    <div class="notebook-tabs" id="notebookTabs">
        <div class="notebook-tab active" data-tab="cpu">CPU分析</div>
        <div class="notebook-tab" data-tab="memory">内存分析</div>
        <div class="notebook-tab" data-tab="performance">性能分析</div>
    </div>

    <div class="notebook-content" id="notebookContent">
        <!-- CPU分析标签页 -->
        <div id="cpu-tab" class="tab-content">
            <div class="tkinter-labelframe">
                <div class="tkinter-labelframe-title">文件选择</div>
                <div class="mb-2">
                    <label style="font-size: 11px;">数据文件:</label>
                </div>
                <div class="file-entry-frame">
                    <input type="text" class="tkinter-entry" id="cpuFilePath" placeholder="选择CPU数据文件">
                    <button class="tkinter-button" onclick="browseFile('cpu')">浏览</button>
                </div>
                <div class="control-frame">
                    <button class="tkinter-button" onclick="analyzeCPU()">分析CPU数据</button>
                    <button class="tkinter-button" onclick="copyCPUChart()">复制图表</button>
                    <button class="tkinter-button" onclick="clearCPUChart()">清除图表</button>
                </div>
            </div>
            
            <div class="tkinter-labelframe">
                <div class="tkinter-labelframe-title">CPU使用率图表</div>
                <div class="chart-frame" id="cpuChart">
                    <div style="text-align: center; padding: 50px; color: #666;">
                        CPU分析图表将在这里显示
                    </div>
                </div>
            </div>
            
            <div class="tkinter-labelframe">
                <div class="tkinter-labelframe-title">分析结果</div>
                <textarea class="tkinter-text" id="cpuResults" readonly>CPU分析结果将在这里显示...</textarea>
            </div>
        </div>
    </div>
</div>

<!-- 文件上传表单 -->
<input type="file" id="fileInput" style="display: none;" accept=".txt,.json,.log">
<input type="file" id="directoryInput" style="display: none;" webkitdirectory multiple>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.2.1/dist/chartjs-plugin-annotation.min.js"></script>
<script>
// 全局变量
let currentTab = 'cpu';
let charts = {};
let uploadedFiles = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    loadTabContent(currentTab);
});

// 初始化标签页功能
function initializeTabs() {
    const tabs = document.querySelectorAll('.notebook-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
            // 切换内容
            currentTab = this.dataset.tab;
            loadTabContent(currentTab);
        });
    });
}

// 加载标签页内容
function loadTabContent(tabName) {
    const content = document.getElementById('notebookContent');
    
    if (tabName === 'cpu') {
        content.innerHTML = createCPUTabContent();
    } else if (tabName === 'memory') {
        content.innerHTML = createMemoryTabContent();
    } else if (tabName === 'performance') {
        content.innerHTML = createPerformanceTabContent();
    }
}

// 创建CPU标签页内容
function createCPUTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="cpuFilePath" placeholder="选择CPU数据文件">
                <button class="tkinter-button" onclick="browseFile('cpu')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeCPU()">分析CPU数据</button>
                <button class="tkinter-button" onclick="copyCPUChart()">复制图表</button>
                <button class="tkinter-button" onclick="clearCPUChart()">清除图表</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">CPU使用率图表</div>
            <div class="chart-frame" id="cpuChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    CPU分析图表将在这里显示
                </div>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="cpuResults" readonly>CPU分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 创建内存标签页内容
function createMemoryTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="memoryFilePath" placeholder="选择内存数据文件">
                <button class="tkinter-button" onclick="browseFile('memory')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeMemory()">分析内存数据</button>
                <button class="tkinter-button" onclick="copyMemoryChart()">复制图表</button>
                <button class="tkinter-button" onclick="clearMemoryChart()">清除图表</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">内存占用图表</div>
            <div class="chart-frame" id="memoryChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    内存分析图表将在这里显示
                </div>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="memoryResults" readonly>内存分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 创建性能标签页内容
function createPerformanceTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">文件选择</div>
            <div class="mb-2">
                <label style="font-size: 11px;">数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="performanceFilePath" placeholder="选择性能数据文件">
                <button class="tkinter-button" onclick="browseFile('performance')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzePerformance()">分析性能数据</button>
                <button class="tkinter-button" onclick="copyPerformanceChart()">复制图表</button>
                <button class="tkinter-button" onclick="clearPerformanceChart()">清除图表</button>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">性能分析图表</div>
            <div class="chart-frame" id="performanceChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    性能分析图表将在这里显示
                </div>
            </div>
        </div>
        
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">分析结果</div>
            <textarea class="tkinter-text" id="performanceResults" readonly>性能分析结果将在这里显示...</textarea>
        </div>
    `;
}

// 文件浏览功能
function browseFile(type) {
    const input = document.getElementById('fileInput');
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const pathInput = document.getElementById(type + 'FilePath');
            if (pathInput) {
                pathInput.value = file.name;
                uploadSingleFile(file, type);
            }
        }
    };
    input.click();
}

// 目录浏览功能
function browseDirectory() {
    const input = document.getElementById('directoryInput');
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            document.getElementById('resultDirPath').value = files[0].webkitRelativePath.split('/')[0];
            uploadMultipleFiles(files);
        }
    };
    input.click();
}

// 上传单个文件
function uploadSingleFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    fetch('/api/c_analysis/upload_file', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedFiles[type] = data.filepath;
            console.log('文件上传成功:', data.filepath);
        } else {
            alert('文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// 上传多个文件
function uploadMultipleFiles(files) {
    const formData = new FormData();
    files.forEach(file => {
        formData.append('files', file);
    });
    
    fetch('/api/c_analysis/upload_directory', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedFiles = data.file_paths;
            // 自动填充文件路径
            fillFilePathsFromBatch(data.file_paths);
            console.log('批量文件上传成功');
        } else {
            alert('批量文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// 从批量分析结果填充文件路径
function fillFilePathsFromBatch(filePaths) {
    if (filePaths.cpu) {
        const cpuInput = document.getElementById('cpuFilePath');
        if (cpuInput) cpuInput.value = filePaths.cpu.split('/').pop();
    }
    if (filePaths.memory) {
        const memoryInput = document.getElementById('memoryFilePath');
        if (memoryInput) memoryInput.value = filePaths.memory.split('/').pop();
    }
    if (filePaths.performance) {
        const performanceInput = document.getElementById('performanceFilePath');
        if (performanceInput) performanceInput.value = filePaths.performance.split('/').pop();
    }
}

// 分析函数
function analyzeCPU() {
    if (!uploadedFiles.cpu) {
        alert('请先选择CPU数据文件');
        return;
    }
    performAnalysis('cpu', 'cpuResults', 'cpuChart');
}

function analyzeMemory() {
    if (!uploadedFiles.memory) {
        alert('请先选择内存数据文件');
        return;
    }
    performAnalysis('memory', 'memoryResults', 'memoryChart');
}

function analyzePerformance() {
    if (!uploadedFiles.performance) {
        alert('请先选择性能数据文件');
        return;
    }
    performAnalysis('performance', 'performanceResults', 'performanceChart');
}

// 批量分析
function batchAnalyze() {
    if (Object.keys(uploadedFiles).length === 0) {
        alert('请先选择结果目录');
        return;
    }
    
    fetch('/api/c_analysis/batch_analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            file_paths: uploadedFiles
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示所有分析结果
            if (data.results.cpu) {
                displayAnalysisResult('cpu', data.results.cpu, 'cpuResults', 'cpuChart');
            }
            if (data.results.memory) {
                displayAnalysisResult('memory', data.results.memory, 'memoryResults', 'memoryChart');
            }
            if (data.results.performance) {
                displayAnalysisResult('performance', data.results.performance, 'performanceResults', 'performanceChart');
            }
            alert(data.message);
        } else {
            alert('批量分析失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('批量分析过程中发生错误: ' + error.message);
    });
}

// 执行分析
function performAnalysis(analysisType, resultsId, chartId) {
    const resultsElement = document.getElementById(resultsId);
    const chartElement = document.getElementById(chartId);
    
    // 显示分析中状态
    if (resultsElement) {
        resultsElement.value = '正在分析中，请稍候...';
    }
    
    fetch('/api/c_analysis/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            analysis_type: analysisType,
            file_path: uploadedFiles[analysisType]
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResult(analysisType, data, resultsId, chartId);
        } else {
            if (resultsElement) {
                resultsElement.value = '分析失败: ' + data.message;
            }
        }
    })
    .catch(error => {
        if (resultsElement) {
            resultsElement.value = '分析过程中发生错误: ' + error.message;
        }
    });
}

// 显示分析结果
function displayAnalysisResult(analysisType, data, resultsId, chartId) {
    const resultsElement = document.getElementById(resultsId);
    const chartElement = document.getElementById(chartId);
    
    // 显示结果文本
    if (resultsElement && data.result_text) {
        resultsElement.value = data.result_text;
    }
    
    // 显示图表
    if (chartElement && data.chart) {
        displayChart(chartId, data.chart);
    }
}

// 显示图表
function displayChart(chartId, chartData) {
    const chartContainer = document.getElementById(chartId);
    chartContainer.innerHTML = '<canvas id="' + chartId + 'Canvas"></canvas>';
    
    const ctx = document.getElementById(chartId + 'Canvas').getContext('2d');
    if (charts[chartId]) {
        charts[chartId].destroy();
    }
    
    // 注册插件
    Chart.register(ChartjsPluginAnnotation);
    
    charts[chartId] = new Chart(ctx, chartData);
}

// 清除图表函数
function clearCPUChart() {
    clearChart('cpuChart', 'cpuResults', 'CPU分析图表将在这里显示', 'CPU分析结果将在这里显示...');
}

function clearMemoryChart() {
    clearChart('memoryChart', 'memoryResults', '内存分析图表将在这里显示', '内存分析结果将在这里显示...');
}

function clearPerformanceChart() {
    clearChart('performanceChart', 'performanceResults', '性能分析图表将在这里显示', '性能分析结果将在这里显示...');
}

function clearChart(chartId, resultsId, chartPlaceholder, resultsPlaceholder) {
    const chartElement = document.getElementById(chartId);
    const resultsElement = document.getElementById(resultsId);
    
    if (chartElement) {
        chartElement.innerHTML = `<div style="text-align: center; padding: 50px; color: #666;">${chartPlaceholder}</div>`;
    }
    if (resultsElement) {
        resultsElement.value = resultsPlaceholder;
    }
    
    if (charts[chartId]) {
        charts[chartId].destroy();
        delete charts[chartId];
    }
}

// 复制图表函数
function copyCPUChart() {
    copyChart('cpuChart');
}

function copyMemoryChart() {
    copyChart('memoryChart');
}

function copyPerformanceChart() {
    copyChart('performanceChart');
}

function copyChart(chartId) {
    if (charts[chartId]) {
        const canvas = document.getElementById(chartId + 'Canvas');
        canvas.toBlob(function(blob) {
            const item = new ClipboardItem({ "image/png": blob });
            navigator.clipboard.write([item]).then(function() {
                alert('图表已复制到剪贴板');
            }).catch(function(err) {
                alert('复制失败: ' + err);
            });
        });
    } else {
        alert('没有可复制的图表');
    }
}
</script>
{% endblock %}
