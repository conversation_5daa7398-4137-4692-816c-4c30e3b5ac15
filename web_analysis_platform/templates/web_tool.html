{% extends "base.html" %}

{% block title %}{{ tool_name }} - 网页版{% endblock %}

{% block extra_css %}
<style>
/* 模拟tkinter界面样式 */
.tkinter-frame {
    border: 1px solid #d0d0d0;
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
}

.tkinter-labelframe {
    border: 2px groove #d0d0d0;
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
}

.tkinter-labelframe-title {
    position: absolute;
    top: -10px;
    left: 10px;
    background: #f0f0f0;
    padding: 0 5px;
    font-weight: bold;
    font-size: 12px;
}

.tkinter-button {
    background: #e1e1e1;
    border: 1px outset #d0d0d0;
    padding: 4px 8px;
    margin: 2px;
    font-size: 11px;
    cursor: pointer;
}

.tkinter-button:hover {
    background: #e8e8e8;
}

.tkinter-button:active {
    border: 1px inset #d0d0d0;
}

.tkinter-entry {
    border: 1px inset #d0d0d0;
    padding: 2px;
    background: white;
    font-size: 11px;
}

.tkinter-text {
    border: 1px inset #d0d0d0;
    background: white;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    padding: 5px;
    white-space: pre-wrap;
    overflow-y: auto;
}

.notebook-tabs {
    border-bottom: 1px solid #d0d0d0;
    background: #f0f0f0;
    margin-bottom: 0;
}

.notebook-tab {
    display: inline-block;
    padding: 6px 12px;
    background: #e1e1e1;
    border: 1px solid #d0d0d0;
    border-bottom: none;
    margin-right: 2px;
    cursor: pointer;
    font-size: 11px;
}

.notebook-tab.active {
    background: #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: -1px;
}

.notebook-content {
    border: 1px solid #d0d0d0;
    border-top: none;
    background: #f0f0f0;
    padding: 10px;
    min-height: 500px;
}

.chart-frame {
    border: 2px groove #d0d0d0;
    background: white;
    padding: 10px;
    margin: 10px 0;
    min-height: 300px;
}

.file-entry-frame {
    display: flex;
    align-items: center;
    margin: 5px 0;
}

.file-entry-frame input {
    flex: 1;
    margin-right: 5px;
}

.control-frame {
    margin: 10px 0;
}

.control-frame button {
    margin-right: 5px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 标题栏 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>{{ tool_name }} - 网页版</h4>
        <a href="{{ url_for('tool_interface', tool_id=tool_id) }}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回
        </a>
    </div>

    {% if tool_id == 'c_analysis' %}
    <!-- C标准分析工具界面 -->
    <!-- 统一目录选择区域 -->
    <div class="tkinter-labelframe">
        <div class="tkinter-labelframe-title">统一目录选择</div>
        <div class="mb-2">
            <label style="font-size: 11px;">选择结果目录:</label>
        </div>
        <div class="file-entry-frame">
            <input type="text" class="tkinter-entry" id="resultDirPath" placeholder="选择包含结果文件的目录">
            <button class="tkinter-button" onclick="browseDirectory()">浏览</button>
        </div>
        <div class="mt-2">
            <button class="tkinter-button" onclick="batchAnalyze()">批量分析</button>
        </div>
    </div>

    <!-- 分隔线 -->
    <hr style="border-top: 1px solid #d0d0d0; margin: 10px 0;">

    {% else %}
    <!-- Java标准化分析工具界面 -->
    <!-- 批量分析区域 -->
    <div class="tkinter-frame">
        <div class="file-entry-frame">
            <input type="text" class="tkinter-entry" id="batchPath" placeholder="输入批量分析路径">
            <button class="tkinter-button" onclick="batchAnalyze()">批量分析</button>
        </div>
    </div>
    {% endif %}

    <!-- 标签页区域 -->
    <div class="notebook-tabs" id="notebookTabs">
        {% if tool_id == 'c_analysis' %}
        <div class="notebook-tab active" data-tab="cpu">CPU分析</div>
        <div class="notebook-tab" data-tab="memory">内存分析</div>
        <div class="notebook-tab" data-tab="performance">性能分析</div>
        {% else %}
        <div class="notebook-tab active" data-tab="cpu">CPU分析</div>
        <div class="notebook-tab" data-tab="memory">内存分析</div>
        <div class="notebook-tab" data-tab="performance">性能分析</div>
        <div class="notebook-tab" data-tab="encryption">模型加密分析</div>
        {% endif %}
    </div>

    <div class="notebook-content" id="notebookContent">
        <!-- 标签页内容将通过JavaScript动态加载 -->
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 全局变量
let currentTab = 'cpu';
let analysisResults = {};
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    loadTabContent(currentTab);
});

// 初始化标签页功能
function initializeTabs() {
    const tabs = document.querySelectorAll('.notebook-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
            // 切换内容
            currentTab = this.dataset.tab;
            loadTabContent(currentTab);
        });
    });
}

// 加载标签页内容
function loadTabContent(tabName) {
    const content = document.getElementById('notebookContent');

    {% if tool_id == 'c_analysis' %}
    // C标准分析工具的标签页内容
    if (tabName === 'cpu') {
        content.innerHTML = createCPUTabContent();
    } else if (tabName === 'memory') {
        content.innerHTML = createMemoryTabContent();
    } else if (tabName === 'performance') {
        content.innerHTML = createPerformanceTabContent();
    }
    {% else %}
    // Java标准化分析工具的标签页内容
    if (tabName === 'cpu') {
        content.innerHTML = createJavaCPUTabContent();
    } else if (tabName === 'memory') {
        content.innerHTML = createJavaMemoryTabContent();
    } else if (tabName === 'performance') {
        content.innerHTML = createJavaPerformanceTabContent();
    } else if (tabName === 'encryption') {
        content.innerHTML = createJavaEncryptionTabContent();
    }
    {% endif %}
}

{% if tool_id == 'c_analysis' %}
// C标准分析工具的标签页内容创建函数
function createCPUTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">CPU分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择CPU文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="cpuFilePath" placeholder="选择CPU数据文件">
                <button class="tkinter-button" onclick="browseFile('cpu')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeCPU()">分析CPU</button>
                <button class="tkinter-button" onclick="clearCPUResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="cpuChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    CPU分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="cpuResults" style="height: 200px;">
                CPU分析结果将在这里显示...
            </div>
        </div>
    `;
}

function createMemoryTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">内存分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择内存文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="memoryFilePath" placeholder="选择内存数据文件">
                <button class="tkinter-button" onclick="browseFile('memory')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeMemory()">分析内存</button>
                <button class="tkinter-button" onclick="clearMemoryResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="memoryChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    内存分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="memoryResults" style="height: 200px;">
                内存分析结果将在这里显示...
            </div>
        </div>
    `;
}

function createPerformanceTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">性能分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择性能文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="performanceFilePath" placeholder="选择性能数据文件">
                <button class="tkinter-button" onclick="browseFile('performance')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzePerformance()">分析性能</button>
                <button class="tkinter-button" onclick="clearPerformanceResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="performanceChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    性能分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="performanceResults" style="height: 200px;">
                性能分析结果将在这里显示...
            </div>
        </div>
    `;
}
{% else %}
// Java标准化分析工具的标签页内容创建函数
function createJavaCPUTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">CPU分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择CPU数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="javaCpuFilePath" placeholder="选择CPU数据文件">
                <button class="tkinter-button" onclick="browseFile('javaCpu')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeJavaCPU()">分析CPU</button>
                <button class="tkinter-button" onclick="clearJavaCPUResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="javaCpuChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    CPU分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="javaCpuResults" style="height: 200px;">
                CPU分析结果将在这里显示...
            </div>
        </div>
    `;
}

function createJavaMemoryTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">内存分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择内存数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="javaMemoryFilePath" placeholder="选择内存数据文件">
                <button class="tkinter-button" onclick="browseFile('javaMemory')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeJavaMemory()">分析内存</button>
                <button class="tkinter-button" onclick="clearJavaMemoryResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="javaMemoryChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    内存分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="javaMemoryResults" style="height: 200px;">
                内存分析结果将在这里显示...
            </div>
        </div>
    `;
}

function createJavaPerformanceTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">性能分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择性能数据文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="javaPerformanceFilePath" placeholder="选择性能数据文件">
                <button class="tkinter-button" onclick="browseFile('javaPerformance')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeJavaPerformance()">分析性能</button>
                <button class="tkinter-button" onclick="clearJavaPerformanceResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="javaPerformanceChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    性能分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="javaPerformanceResults" style="height: 200px;">
                性能分析结果将在这里显示...
            </div>
        </div>
    `;
}

function createJavaEncryptionTabContent() {
    return `
        <div class="tkinter-labelframe">
            <div class="tkinter-labelframe-title">模型加密分析</div>
            <div class="mb-2">
                <label style="font-size: 11px;">选择加密日志文件:</label>
            </div>
            <div class="file-entry-frame">
                <input type="text" class="tkinter-entry" id="javaEncryptionFilePath" placeholder="选择加密日志文件">
                <button class="tkinter-button" onclick="browseFile('javaEncryption')">浏览</button>
            </div>
            <div class="control-frame">
                <button class="tkinter-button" onclick="analyzeJavaEncryption()">分析加密</button>
                <button class="tkinter-button" onclick="clearJavaEncryptionResults()">清除结果</button>
            </div>
            <div class="chart-frame" id="javaEncryptionChart">
                <div style="text-align: center; padding: 50px; color: #666;">
                    加密分析图表将在这里显示
                </div>
            </div>
            <div class="tkinter-text" id="javaEncryptionResults" style="height: 200px;">
                加密分析结果将在这里显示...
            </div>
        </div>
    `;
}
{% endif %}

// 文件浏览功能
function browseFile(type) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.log,.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const pathInput = document.getElementById(type + 'FilePath');
            if (pathInput) {
                pathInput.value = file.name;
                // 上传文件
                uploadSingleFile(file, type);
            }
        }
    };
    input.click();
}

// 目录浏览功能（批量分析）
function browseDirectory() {
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.multiple = true;
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            document.getElementById('resultDirPath').value = files[0].webkitRelativePath.split('/')[0];
            // 上传所有文件
            uploadMultipleFiles(files);
        }
    };
    input.click();
}

// 上传单个文件
function uploadSingleFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    formData.append('tool_id', '{{ tool_id }}');

    fetch('/api/upload_single_file', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('文件上传成功:', data.filepath);
        } else {
            alert('文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// 上传多个文件
function uploadMultipleFiles(files) {
    const formData = new FormData();
    files.forEach(file => {
        formData.append('files', file);
    });
    formData.append('tool_id', '{{ tool_id }}');

    fetch('/api/upload_multiple_files', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('批量文件上传成功');
        } else {
            alert('批量文件上传失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中发生错误: ' + error.message);
    });
}

// C标准分析工具的分析函数
function analyzeCPU() {
    const filePath = document.getElementById('cpuFilePath').value;
    if (!filePath) {
        alert('请先选择CPU数据文件');
        return;
    }

    performAnalysis('cpu', 'cpuResults', 'cpuChart');
}

function analyzeMemory() {
    const filePath = document.getElementById('memoryFilePath').value;
    if (!filePath) {
        alert('请先选择内存数据文件');
        return;
    }

    performAnalysis('memory', 'memoryResults', 'memoryChart');
}

function analyzePerformance() {
    const filePath = document.getElementById('performanceFilePath').value;
    if (!filePath) {
        alert('请先选择性能数据文件');
        return;
    }

    performAnalysis('performance', 'performanceResults', 'performanceChart');
}

// Java标准化分析工具的分析函数
function analyzeJavaCPU() {
    const filePath = document.getElementById('javaCpuFilePath').value;
    if (!filePath) {
        alert('请先选择CPU数据文件');
        return;
    }

    performAnalysis('javaCpu', 'javaCpuResults', 'javaCpuChart');
}

function analyzeJavaMemory() {
    const filePath = document.getElementById('javaMemoryFilePath').value;
    if (!filePath) {
        alert('请先选择内存数据文件');
        return;
    }

    performAnalysis('javaMemory', 'javaMemoryResults', 'javaMemoryChart');
}

function analyzeJavaPerformance() {
    const filePath = document.getElementById('javaPerformanceFilePath').value;
    if (!filePath) {
        alert('请先选择性能数据文件');
        return;
    }

    performAnalysis('javaPerformance', 'javaPerformanceResults', 'javaPerformanceChart');
}

function analyzeJavaEncryption() {
    const filePath = document.getElementById('javaEncryptionFilePath').value;
    if (!filePath) {
        alert('请先选择加密日志文件');
        return;
    }

    performAnalysis('javaEncryption', 'javaEncryptionResults', 'javaEncryptionChart');
}

// 批量分析
function batchAnalyze() {
    {% if tool_id == 'c_analysis' %}
    const dirPath = document.getElementById('resultDirPath').value;
    if (!dirPath) {
        alert('请先选择结果目录');
        return;
    }
    {% else %}
    const batchPath = document.getElementById('batchPath').value;
    if (!batchPath) {
        alert('请输入批量分析路径');
        return;
    }
    {% endif %}

    performBatchAnalysis();
}

// 执行分析
function performAnalysis(analysisType, resultsId, chartId) {
    const resultsElement = document.getElementById(resultsId);
    const chartElement = document.getElementById(chartId);

    // 显示分析中状态
    resultsElement.textContent = '正在分析中，请稍候...';

    fetch('/api/perform_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tool_id: '{{ tool_id }}',
            analysis_type: analysisType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示分析结果
            resultsElement.textContent = data.results.text;

            // 显示图表
            if (data.results.chart) {
                displayChart(chartId, data.results.chart);
            }
        } else {
            resultsElement.textContent = '分析失败: ' + data.message;
        }
    })
    .catch(error => {
        resultsElement.textContent = '分析过程中发生错误: ' + error.message;
    });
}

// 执行批量分析
function performBatchAnalysis() {
    fetch('/api/perform_batch_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tool_id: '{{ tool_id }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 自动填充文件路径到各个标签页
            fillFilePathsFromBatch(data.file_paths);
            alert(data.message);
        } else {
            alert('批量分析失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('批量分析过程中发生错误: ' + error.message);
    });
}

// 从批量分析结果填充文件路径
function fillFilePathsFromBatch(filePaths) {
    {% if tool_id == 'c_analysis' %}
    // C工具的文件路径填充
    if (filePaths.cpu) {
        const cpuInput = document.getElementById('cpuFilePath');
        if (cpuInput) cpuInput.value = filePaths.cpu.filename;
    }
    if (filePaths.memory) {
        const memoryInput = document.getElementById('memoryFilePath');
        if (memoryInput) memoryInput.value = filePaths.memory.filename;
    }
    if (filePaths.performance) {
        const performanceInput = document.getElementById('performanceFilePath');
        if (performanceInput) performanceInput.value = filePaths.performance.filename;
    }
    {% else %}
    // Java工具的文件路径填充
    if (filePaths.javaCpu) {
        const javaCpuInput = document.getElementById('javaCpuFilePath');
        if (javaCpuInput) javaCpuInput.value = filePaths.javaCpu.filename;
    }
    if (filePaths.javaMemory) {
        const javaMemoryInput = document.getElementById('javaMemoryFilePath');
        if (javaMemoryInput) javaMemoryInput.value = filePaths.javaMemory.filename;
    }
    if (filePaths.javaPerformance) {
        const javaPerformanceInput = document.getElementById('javaPerformanceFilePath');
        if (javaPerformanceInput) javaPerformanceInput.value = filePaths.javaPerformance.filename;
    }
    if (filePaths.javaEncryption) {
        const javaEncryptionInput = document.getElementById('javaEncryptionFilePath');
        if (javaEncryptionInput) javaEncryptionInput.value = filePaths.javaEncryption.filename;
    }
    {% endif %}
}

// 显示图表
function displayChart(chartId, chartData) {
    const chartContainer = document.getElementById(chartId);
    chartContainer.innerHTML = '<canvas id="' + chartId + 'Canvas"></canvas>';

    const ctx = document.getElementById(chartId + 'Canvas').getContext('2d');
    if (charts[chartId]) {
        charts[chartId].destroy();
    }
    charts[chartId] = new Chart(ctx, chartData);
}

// 清除结果函数
function clearCPUResults() {
    document.getElementById('cpuResults').textContent = 'CPU分析结果将在这里显示...';
    document.getElementById('cpuChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">CPU分析图表将在这里显示</div>';
}

function clearMemoryResults() {
    document.getElementById('memoryResults').textContent = '内存分析结果将在这里显示...';
    document.getElementById('memoryChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">内存分析图表将在这里显示</div>';
}

function clearPerformanceResults() {
    document.getElementById('performanceResults').textContent = '性能分析结果将在这里显示...';
    document.getElementById('performanceChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">性能分析图表将在这里显示</div>';
}

function clearJavaCPUResults() {
    document.getElementById('javaCpuResults').textContent = 'CPU分析结果将在这里显示...';
    document.getElementById('javaCpuChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">CPU分析图表将在这里显示</div>';
}

function clearJavaMemoryResults() {
    document.getElementById('javaMemoryResults').textContent = '内存分析结果将在这里显示...';
    document.getElementById('javaMemoryChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">内存分析图表将在这里显示</div>';
}

function clearJavaPerformanceResults() {
    document.getElementById('javaPerformanceResults').textContent = '性能分析结果将在这里显示...';
    document.getElementById('javaPerformanceChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">性能分析图表将在这里显示</div>';
}

function clearJavaEncryptionResults() {
    document.getElementById('javaEncryptionResults').textContent = '加密分析结果将在这里显示...';
    document.getElementById('javaEncryptionChart').innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">加密分析图表将在这里显示</div>';
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    uploadFiles(files);
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

// 处理文件拖拽
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    
    const files = Array.from(event.dataTransfer.files);
    uploadFiles(files);
}

// 上传文件
function uploadFiles(files) {
    if (files.length === 0) return;
    
    const formData = new FormData();
    files.forEach(file => {
        formData.append('files', file);
    });
    formData.append('tool_id', '{{ tool_id }}');
    
    // 显示进度条
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('uploadProgress');
    const progressText = document.getElementById('progressText');
    
    progressContainer.style.display = 'block';
    progressText.textContent = '正在上传文件...';
    
    // 使用XMLHttpRequest以支持进度显示
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressText.textContent = `上传进度: ${Math.round(percentComplete)}%`;
        }
    });
    
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                uploadedFiles = uploadedFiles.concat(response.files);
                updateFileList();
                updateAnalysisButton();
                progressText.textContent = '上传完成！';
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 2000);
            } else {
                showError('上传失败: ' + response.message);
            }
        } else {
            showError('上传失败，请重试');
        }
    });
    
    xhr.addEventListener('error', function() {
        showError('网络错误，上传失败');
    });
    
    xhr.open('POST', '/api/upload_files');
    xhr.send(formData);
}

// 更新文件列表显示
function updateFileList() {
    const fileList = document.getElementById('fileList');
    
    if (uploadedFiles.length === 0) {
        fileList.innerHTML = '';
        return;
    }
    
    let html = '<h6>已上传文件:</h6><div class="list-group">';
    uploadedFiles.forEach((file, index) => {
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-alt me-2"></i>${file.filename}
                    <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });
    html += '</div>';
    
    fileList.innerHTML = html;
}

// 移除文件
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFileList();
    updateAnalysisButton();
}

// 更新分析按钮状态
function updateAnalysisButton() {
    const startButton = document.getElementById('startAnalysis');
    startButton.disabled = uploadedFiles.length === 0;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 初始化分析功能
function initializeAnalysis() {
    document.getElementById('startAnalysis').addEventListener('click', startAnalysis);
    document.getElementById('clearFiles').addEventListener('click', clearFiles);
}

// 开始分析
function startAnalysis() {
    if (uploadedFiles.length === 0) {
        showError('请先上传文件');
        return;
    }
    
    // 获取分析选项
    const analysisOptions = getAnalysisOptions();
    
    // 显示加载状态
    const startButton = document.getElementById('startAnalysis');
    const originalText = startButton.innerHTML;
    startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';
    startButton.disabled = true;
    
    // 发送分析请求
    fetch('/api/analyze_files', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tool_id: '{{ tool_id }}',
            files: uploadedFiles,
            options: analysisOptions
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            analysisResults = data.results;
            displayResults();
            document.getElementById('resultsSection').style.display = 'block';
            // 滚动到结果区域
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        } else {
            showError('分析失败: ' + data.message);
        }
    })
    .catch(error => {
        showError('分析过程中发生错误: ' + error.message);
    })
    .finally(() => {
        startButton.innerHTML = originalText;
        startButton.disabled = false;
    });
}

// 获取分析选项
function getAnalysisOptions() {
    const options = {};
    
    {% if tool_id == 'c_analysis' %}
    options.analyzeCPU = document.getElementById('analyzeCPU').checked;
    options.analyzeMemory = document.getElementById('analyzeMemory').checked;
    options.analyzePerformance = document.getElementById('analyzePerformance').checked;
    {% else %}
    options.analyzeCPUMemory = document.getElementById('analyzeCPUMemory').checked;
    options.analyzePerformanceModule = document.getElementById('analyzePerformanceModule').checked;
    options.analyzeEncryption = document.getElementById('analyzeEncryption').checked;
    options.generateReport = document.getElementById('generateReport').checked;
    {% endif %}
    
    return options;
}

// 显示分析结果
function displayResults() {
    displayStats();
    displayCharts();
    displayDetailedResults();
    displayDownloadLinks();
}

// 显示统计概览
function displayStats() {
    const statsGrid = document.getElementById('statsGrid');
    let html = '';
    
    if (analysisResults.stats) {
        Object.entries(analysisResults.stats).forEach(([key, value]) => {
            html += `
                <div class="stat-card">
                    <div class="stat-value">${value}</div>
                    <div class="stat-label">${key}</div>
                </div>
            `;
        });
    }
    
    statsGrid.innerHTML = html;
}

// 显示图表
function displayCharts() {
    if (analysisResults.charts) {
        // 清除现有图表
        Object.values(charts).forEach(chart => chart.destroy());
        charts = {};
        
        // 创建新图表
        if (analysisResults.charts.chart1) {
            createChart('chart1', analysisResults.charts.chart1);
        }
        if (analysisResults.charts.chart2) {
            createChart('chart2', analysisResults.charts.chart2);
        }
    }
}

// 创建图表
function createChart(canvasId, chartData) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    charts[canvasId] = new Chart(ctx, chartData);
}

// 显示详细结果
function displayDetailedResults() {
    const detailedResults = document.getElementById('detailedResults');
    let html = '';
    
    if (analysisResults.details) {
        html += '<h5><i class="fas fa-list me-2"></i>详细分析结果</h5>';
        
        analysisResults.details.forEach(detail => {
            html += `
                <div class="result-card">
                    <h6>${detail.title}</h6>
                    <div>${detail.content}</div>
                </div>
            `;
        });
    }
    
    detailedResults.innerHTML = html;
}

// 显示下载链接
function displayDownloadLinks() {
    const downloadLinks = document.getElementById('downloadLinks');
    let html = '';
    
    if (analysisResults.downloads) {
        analysisResults.downloads.forEach(download => {
            html += `
                <a href="${download.url}" class="btn btn-outline-primary me-2 mb-2" download>
                    <i class="fas fa-download me-1"></i>${download.name}
                </a>
            `;
        });
    }
    
    downloadLinks.innerHTML = html;
}

// 清空文件
function clearFiles() {
    uploadedFiles = [];
    updateFileList();
    updateAnalysisButton();
    document.getElementById('resultsSection').style.display = 'none';
    
    // 清除图表
    Object.values(charts).forEach(chart => chart.destroy());
    charts = {};
}

// 显示错误信息
function showError(message) {
    // 创建错误提示
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
