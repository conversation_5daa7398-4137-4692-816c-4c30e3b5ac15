{% extends "base.html" %}

{% block title %}{{ tool_name }} - 网页版{% endblock %}

{% block extra_css %}
<style>
.analysis-section {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #f8f9fa;
}

.file-drop-zone {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: #0056b3;
    background: #e3f2fd;
}

.file-drop-zone.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.result-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: white;
}

.progress-container {
    display: none;
    margin-top: 20px;
}

.chart-container {
    height: 400px;
    margin: 20px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title mb-1">
                            <i class="fas fa-{{ 'code' if tool_id == 'c_analysis' else 'java' }} me-2"></i>{{ tool_name }} - 网页版
                        </h2>
                        <p class="card-text text-muted mb-0">在线分析工具，功能与桌面版完全一致</p>
                    </div>
                    <a href="{{ url_for('tool_interface', tool_id=tool_id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件上传区域 -->
<div class="row">
    <div class="col-12">
        <div class="analysis-section">
            <h4><i class="fas fa-upload me-2"></i>文件上传</h4>
            <p class="text-muted">
                {% if tool_id == 'c_analysis' %}
                支持上传 stability_CPU_*.txt, stability_Memory_*.txt, stability_Detect_*.txt 文件
                {% else %}
                支持上传 CPU/内存数据文件、性能数据文件、模型加密日志文件
                {% endif %}
            </p>
            
            <div class="file-drop-zone" id="fileDropZone">
                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                <h5>拖拽文件到此处或点击选择文件</h5>
                <p class="text-muted">支持多文件上传，最大100MB</p>
                <input type="file" id="fileInput" multiple accept=".txt,.json,.log" style="display: none;">
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress">
                    <div class="progress-bar" id="uploadProgress" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted mt-2" id="progressText">准备上传...</small>
            </div>
            
            <div id="fileList" class="mt-3"></div>
        </div>
    </div>
</div>

<!-- 分析控制区域 -->
<div class="row">
    <div class="col-12">
        <div class="analysis-section">
            <h4><i class="fas fa-cogs me-2"></i>分析设置</h4>
            
            {% if tool_id == 'c_analysis' %}
            <div class="row">
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzeCPU" checked>
                        <label class="form-check-label" for="analyzeCPU">
                            <i class="fas fa-microchip me-1"></i>CPU分析
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzeMemory" checked>
                        <label class="form-check-label" for="analyzeMemory">
                            <i class="fas fa-memory me-1"></i>内存分析
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzePerformance" checked>
                        <label class="form-check-label" for="analyzePerformance">
                            <i class="fas fa-tachometer-alt me-1"></i>性能分析
                        </label>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="row">
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzeCPUMemory" checked>
                        <label class="form-check-label" for="analyzeCPUMemory">
                            <i class="fas fa-microchip me-1"></i>CPU/内存统计
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzePerformanceModule" checked>
                        <label class="form-check-label" for="analyzePerformanceModule">
                            <i class="fas fa-chart-line me-1"></i>性能模块分析
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="analyzeEncryption" checked>
                        <label class="form-check-label" for="analyzeEncryption">
                            <i class="fas fa-lock me-1"></i>加密检测分析
                        </label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="generateReport" checked>
                        <label class="form-check-label" for="generateReport">
                            <i class="fas fa-file-alt me-1"></i>生成报告
                        </label>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="mt-3">
                <button class="btn btn-primary btn-lg" id="startAnalysis" disabled>
                    <i class="fas fa-play me-2"></i>开始分析
                </button>
                <button class="btn btn-secondary ms-2" id="clearFiles">
                    <i class="fas fa-trash me-2"></i>清空文件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 分析结果区域 -->
<div class="row" id="resultsSection" style="display: none;">
    <div class="col-12">
        <div class="analysis-section">
            <h4><i class="fas fa-chart-bar me-2"></i>分析结果</h4>
            
            <!-- 统计概览 -->
            <div class="stats-grid" id="statsGrid">
                <!-- 动态生成统计卡片 -->
            </div>
            
            <!-- 图表区域 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="chart1"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="chart2"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 详细结果 -->
            <div id="detailedResults">
                <!-- 动态生成详细结果 -->
            </div>
            
            <!-- 下载区域 -->
            <div class="mt-4">
                <h5><i class="fas fa-download me-2"></i>下载结果</h5>
                <div id="downloadLinks">
                    <!-- 动态生成下载链接 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 全局变量
let uploadedFiles = [];
let analysisResults = {};
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload();
    initializeAnalysis();
});

// 初始化文件上传功能
function initializeFileUpload() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');
    
    // 点击上传区域
    dropZone.addEventListener('click', () => fileInput.click());
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽功能
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleFileDrop);
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    uploadFiles(files);
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

// 处理文件拖拽
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    
    const files = Array.from(event.dataTransfer.files);
    uploadFiles(files);
}

// 上传文件
function uploadFiles(files) {
    if (files.length === 0) return;
    
    const formData = new FormData();
    files.forEach(file => {
        formData.append('files', file);
    });
    formData.append('tool_id', '{{ tool_id }}');
    
    // 显示进度条
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('uploadProgress');
    const progressText = document.getElementById('progressText');
    
    progressContainer.style.display = 'block';
    progressText.textContent = '正在上传文件...';
    
    // 使用XMLHttpRequest以支持进度显示
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressText.textContent = `上传进度: ${Math.round(percentComplete)}%`;
        }
    });
    
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                uploadedFiles = uploadedFiles.concat(response.files);
                updateFileList();
                updateAnalysisButton();
                progressText.textContent = '上传完成！';
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 2000);
            } else {
                showError('上传失败: ' + response.message);
            }
        } else {
            showError('上传失败，请重试');
        }
    });
    
    xhr.addEventListener('error', function() {
        showError('网络错误，上传失败');
    });
    
    xhr.open('POST', '/api/upload_files');
    xhr.send(formData);
}

// 更新文件列表显示
function updateFileList() {
    const fileList = document.getElementById('fileList');
    
    if (uploadedFiles.length === 0) {
        fileList.innerHTML = '';
        return;
    }
    
    let html = '<h6>已上传文件:</h6><div class="list-group">';
    uploadedFiles.forEach((file, index) => {
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-alt me-2"></i>${file.filename}
                    <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });
    html += '</div>';
    
    fileList.innerHTML = html;
}

// 移除文件
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFileList();
    updateAnalysisButton();
}

// 更新分析按钮状态
function updateAnalysisButton() {
    const startButton = document.getElementById('startAnalysis');
    startButton.disabled = uploadedFiles.length === 0;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 初始化分析功能
function initializeAnalysis() {
    document.getElementById('startAnalysis').addEventListener('click', startAnalysis);
    document.getElementById('clearFiles').addEventListener('click', clearFiles);
}

// 开始分析
function startAnalysis() {
    if (uploadedFiles.length === 0) {
        showError('请先上传文件');
        return;
    }
    
    // 获取分析选项
    const analysisOptions = getAnalysisOptions();
    
    // 显示加载状态
    const startButton = document.getElementById('startAnalysis');
    const originalText = startButton.innerHTML;
    startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>分析中...';
    startButton.disabled = true;
    
    // 发送分析请求
    fetch('/api/analyze_files', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tool_id: '{{ tool_id }}',
            files: uploadedFiles,
            options: analysisOptions
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            analysisResults = data.results;
            displayResults();
            document.getElementById('resultsSection').style.display = 'block';
            // 滚动到结果区域
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        } else {
            showError('分析失败: ' + data.message);
        }
    })
    .catch(error => {
        showError('分析过程中发生错误: ' + error.message);
    })
    .finally(() => {
        startButton.innerHTML = originalText;
        startButton.disabled = false;
    });
}

// 获取分析选项
function getAnalysisOptions() {
    const options = {};
    
    {% if tool_id == 'c_analysis' %}
    options.analyzeCPU = document.getElementById('analyzeCPU').checked;
    options.analyzeMemory = document.getElementById('analyzeMemory').checked;
    options.analyzePerformance = document.getElementById('analyzePerformance').checked;
    {% else %}
    options.analyzeCPUMemory = document.getElementById('analyzeCPUMemory').checked;
    options.analyzePerformanceModule = document.getElementById('analyzePerformanceModule').checked;
    options.analyzeEncryption = document.getElementById('analyzeEncryption').checked;
    options.generateReport = document.getElementById('generateReport').checked;
    {% endif %}
    
    return options;
}

// 显示分析结果
function displayResults() {
    displayStats();
    displayCharts();
    displayDetailedResults();
    displayDownloadLinks();
}

// 显示统计概览
function displayStats() {
    const statsGrid = document.getElementById('statsGrid');
    let html = '';
    
    if (analysisResults.stats) {
        Object.entries(analysisResults.stats).forEach(([key, value]) => {
            html += `
                <div class="stat-card">
                    <div class="stat-value">${value}</div>
                    <div class="stat-label">${key}</div>
                </div>
            `;
        });
    }
    
    statsGrid.innerHTML = html;
}

// 显示图表
function displayCharts() {
    if (analysisResults.charts) {
        // 清除现有图表
        Object.values(charts).forEach(chart => chart.destroy());
        charts = {};
        
        // 创建新图表
        if (analysisResults.charts.chart1) {
            createChart('chart1', analysisResults.charts.chart1);
        }
        if (analysisResults.charts.chart2) {
            createChart('chart2', analysisResults.charts.chart2);
        }
    }
}

// 创建图表
function createChart(canvasId, chartData) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    charts[canvasId] = new Chart(ctx, chartData);
}

// 显示详细结果
function displayDetailedResults() {
    const detailedResults = document.getElementById('detailedResults');
    let html = '';
    
    if (analysisResults.details) {
        html += '<h5><i class="fas fa-list me-2"></i>详细分析结果</h5>';
        
        analysisResults.details.forEach(detail => {
            html += `
                <div class="result-card">
                    <h6>${detail.title}</h6>
                    <div>${detail.content}</div>
                </div>
            `;
        });
    }
    
    detailedResults.innerHTML = html;
}

// 显示下载链接
function displayDownloadLinks() {
    const downloadLinks = document.getElementById('downloadLinks');
    let html = '';
    
    if (analysisResults.downloads) {
        analysisResults.downloads.forEach(download => {
            html += `
                <a href="${download.url}" class="btn btn-outline-primary me-2 mb-2" download>
                    <i class="fas fa-download me-1"></i>${download.name}
                </a>
            `;
        });
    }
    
    downloadLinks.innerHTML = html;
}

// 清空文件
function clearFiles() {
    uploadedFiles = [];
    updateFileList();
    updateAnalysisButton();
    document.getElementById('resultsSection').style.display = 'none';
    
    // 清除图表
    Object.values(charts).forEach(chart => chart.destroy());
    charts = {};
}

// 显示错误信息
function showError(message) {
    // 创建错误提示
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
