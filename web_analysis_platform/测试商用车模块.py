#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商用车标准化测试统计工具模块
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_commercial_vehicle_module():
    """测试商用车工具模块"""
    print("=" * 60)
    print("商用车标准化测试统计工具模块测试")
    print("=" * 60)
    
    # 测试应用导入
    try:
        from app import app
        print("✓ Flask应用导入成功")
    except Exception as e:
        print(f"✗ Flask应用导入失败: {e}")
        return False
    
    # 测试路由注册
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append(rule.rule)
    
    expected_routes = [
        '/tool/commercial_vehicle',
        '/download/commercial_vehicle', 
        '/launch_local/commercial_vehicle'
    ]
    
    print("\n检查路由注册:")
    for route in expected_routes:
        if any(route in r for r in routes):
            print(f"✓ {route}")
        else:
            print(f"✗ {route} 未找到")
    
    # 测试EXE文件存在
    exe_path = '../exe_programs/商用车标准化测试统计工具v2.4.exe'
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    abs_path = os.path.abspath(os.path.join(base_dir, exe_path))
    
    print(f"\n检查EXE文件:")
    print(f"路径: {abs_path}")
    
    if os.path.exists(abs_path):
        size_mb = os.path.getsize(abs_path) / (1024 * 1024)
        print(f"✓ 商用车工具EXE存在 ({size_mb:.1f} MB)")
    else:
        print("✗ 商用车工具EXE不存在")
        return False
    
    # 测试工具配置
    print("\n检查工具配置:")
    
    # 模拟dashboard函数的工具配置
    tools = [
        {
            'id': 'c_analysis',
            'name': 'C标准化分析工具',
            'description': '点击直接启动C标准分析工具GUI',
            'icon': 'fas fa-code',
            'color': 'primary'
        },
        {
            'id': 'java_analysis',
            'name': 'Java标准化分析工具',
            'description': '点击直接启动Java标准化分析工具GUI',
            'icon': 'fab fa-java',
            'color': 'warning'
        },
        {
            'id': 'commercial_vehicle',
            'name': '商用车标准化测试统计工具',
            'description': '点击直接启动商用车标准化测试统计工具GUI',
            'icon': 'fas fa-truck',
            'color': 'success'
        }
    ]
    
    commercial_tool = None
    for tool in tools:
        if tool['id'] == 'commercial_vehicle':
            commercial_tool = tool
            break
    
    if commercial_tool:
        print("✓ 商用车工具配置正确:")
        print(f"  名称: {commercial_tool['name']}")
        print(f"  图标: {commercial_tool['icon']}")
        print(f"  颜色: {commercial_tool['color']}")
        print(f"  描述: {commercial_tool['description']}")
    else:
        print("✗ 商用车工具配置未找到")
        return False
    
    # 测试模板文件
    print("\n检查模板文件:")
    template_files = [
        'templates/tool_interface.html',
        'templates/dashboard.html',
        'templates/admin.html'
    ]
    
    for template in template_files:
        if os.path.exists(template):
            print(f"✓ {template}")
        else:
            print(f"✗ {template}")
    
    print("\n" + "=" * 60)
    print("✅ 商用车工具模块测试完成")
    print("=" * 60)
    
    print("\n功能验证:")
    print("1. 仪表板应显示3个工具（C、Java、商用车）")
    print("2. 商用车工具使用绿色主题和卡车图标")
    print("3. 支持客户端下载和服务器端启动")
    print("4. EXE文件可以正常下载")
    
    print("\n测试步骤:")
    print("1. 启动应用: python app.py")
    print("2. 访问: http://localhost:5000")
    print("3. 登录后查看仪表板")
    print("4. 点击商用车工具测试功能")
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n检查项目文件结构:")
    
    required_files = [
        'app.py',
        'templates/dashboard.html',
        'templates/tool_interface.html',
        '商用车工具模块说明.md'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file}")

def main():
    """主函数"""
    success = test_commercial_vehicle_module()
    test_file_structure()
    
    if success:
        print("\n🎉 商用车工具模块集成成功！")
        print("\n现在可以启动应用测试新功能:")
        print("python app.py")
    else:
        print("\n❌ 商用车工具模块集成存在问题，请检查配置")

if __name__ == '__main__':
    main()
