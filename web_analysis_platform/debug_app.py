#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试应用
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("开始调试...")

try:
    print("1. 导入配置...")
    from config import Config
    print("✓ 配置导入成功")
    
    print("2. 导入认证模块...")
    from auth import require_login, check_credentials
    print("✓ 认证模块导入成功")
    
    print("3. 导入工具管理器...")
    from tools import ToolManager
    print("✓ 工具管理器导入成功")
    
    print("4. 创建工具管理器实例...")
    tool_manager = ToolManager()
    tools = tool_manager.get_all_tools()
    print(f"✓ 工具管理器创建成功，发现 {len(tools)} 个工具")
    
    print("5. 导入Flask应用...")
    from app import app
    print("✓ Flask应用导入成功")
    
    print("6. 检查路由...")
    print("注册的路由:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint}")
    
    print("\n7. 测试启动应用...")
    print("应用配置:")
    print(f"  DEBUG: {app.debug}")
    print(f"  SECRET_KEY: {'已设置' if app.secret_key else '未设置'}")
    
    print("\n✓ 所有检查通过，应用可以启动")
    print("运行命令: python run.py")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()
