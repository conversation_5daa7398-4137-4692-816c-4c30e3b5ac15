#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证模块
"""

from functools import wraps
from flask import session, redirect, url_for, flash
from config import Config

def check_credentials(username, password):
    """检查用户凭据"""
    return username in Config.USERS and Config.USERS[username] == password

def require_login(f):
    """登录装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('请先登录！', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function
