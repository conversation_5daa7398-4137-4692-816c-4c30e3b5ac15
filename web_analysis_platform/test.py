#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

print("测试应用导入...")

try:
    from app import app
    print("✓ 应用导入成功")
    
    print("检查路由...")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint}")
    
    print("检查脚本文件...")
    script_paths = {
        'c_analysis': '../C标准分析工具UI.py',
        'java_analysis': '../Java标准化分析工具UI.py'
    }
    
    for tool_id, script_path in script_paths.items():
        if not os.path.isabs(script_path):
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            abs_path = os.path.abspath(os.path.join(base_dir, script_path))
        else:
            abs_path = script_path
        
        if os.path.exists(abs_path):
            print(f"✓ {tool_id}: {abs_path}")
        else:
            print(f"✗ {tool_id}: {abs_path} (不存在)")
    
    print("\n应用可以启动")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()
