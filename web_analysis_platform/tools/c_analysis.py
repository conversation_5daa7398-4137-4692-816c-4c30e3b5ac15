#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C标准化分析工具包装器
"""

import os
import re
import json
import shutil
from .base_tool import BaseTool

class CAnalysisTool(BaseTool):
    """C标准化分析工具"""
    
    def run_analysis(self, input_dir, output_dir, params=None):
        """
        运行C标准化分析
        
        Args:
            input_dir: 输入文件目录
            output_dir: 输出结果目录
            params: 额外参数
            
        Returns:
            dict: 分析结果
        """
        try:
            # 准备输入文件
            files = self._prepare_files(input_dir)
            
            if not files:
                return self._create_result_summary(
                    success=False,
                    message="没有找到输入文件"
                )
            
            # 识别文件类型
            cpu_file = None
            memory_file = None
            performance_file = None
            
            # 使用正则表达式匹配文件
            cpu_pattern = re.compile(r'stability_CPU_\d{8}_\d{6}\.txt')
            detect_pattern = re.compile(r'stability_Detect_\d{8}_\d{6}\.txt')
            memory_pattern = re.compile(r'stability_Memory_\d{8}_\d{6}\.txt')
            
            for filename, filepath in files.items():
                if cpu_pattern.match(filename):
                    cpu_file = filepath
                elif detect_pattern.match(filename):
                    performance_file = filepath
                elif memory_pattern.match(filename):
                    memory_file = filepath
            
            # 检查是否找到了必要的文件
            found_files = []
            if cpu_file:
                found_files.append(f"CPU文件: {os.path.basename(cpu_file)}")
            if memory_file:
                found_files.append(f"内存文件: {os.path.basename(memory_file)}")
            if performance_file:
                found_files.append(f"性能文件: {os.path.basename(performance_file)}")
            
            if not found_files:
                return self._create_result_summary(
                    success=False,
                    message="没有找到匹配的分析文件（需要stability_CPU_*, stability_Memory_*, stability_Detect_*格式的文件）"
                )
            
            # 创建临时工作目录
            work_dir = os.path.join(output_dir, 'work')
            os.makedirs(work_dir, exist_ok=True)
            
            # 复制文件到工作目录
            work_files = {}
            if cpu_file:
                work_cpu = os.path.join(work_dir, os.path.basename(cpu_file))
                shutil.copy2(cpu_file, work_cpu)
                work_files['cpu'] = work_cpu
            
            if memory_file:
                work_memory = os.path.join(work_dir, os.path.basename(memory_file))
                shutil.copy2(memory_file, work_memory)
                work_files['memory'] = work_memory
            
            if performance_file:
                work_performance = os.path.join(work_dir, os.path.basename(performance_file))
                shutil.copy2(performance_file, work_performance)
                work_files['performance'] = work_performance
            
            # 运行分析脚本
            # 注意：由于原始脚本是GUI应用，我们需要以非交互模式运行
            # 这里我们模拟分析过程，实际项目中可能需要修改原始脚本支持命令行模式
            
            result_data = self._simulate_analysis(work_files)
            
            # 保存结果
            self._save_result(output_dir, result_data, 'c_analysis_result.json')
            
            # 生成结果文件列表
            result_files = ['c_analysis_result.json']
            
            return self._create_result_summary(
                success=True,
                message=f"C标准化分析完成，找到文件: {', '.join(found_files)}",
                data=result_data,
                files=result_files
            )
            
        except Exception as e:
            return self._create_result_summary(
                success=False,
                message=f"分析失败: {str(e)}"
            )
    
    def _simulate_analysis(self, files):
        """
        模拟分析过程
        由于原始脚本是GUI应用，这里提供一个模拟的分析结果
        实际使用时，可以修改原始脚本支持命令行模式
        """
        result = {
            'analysis_type': 'C标准化分析',
            'files_analyzed': [],
            'cpu_analysis': None,
            'memory_analysis': None,
            'performance_analysis': None
        }
        
        # 分析CPU文件
        if 'cpu' in files:
            result['files_analyzed'].append(os.path.basename(files['cpu']))
            result['cpu_analysis'] = self._analyze_cpu_file(files['cpu'])
        
        # 分析内存文件
        if 'memory' in files:
            result['files_analyzed'].append(os.path.basename(files['memory']))
            result['memory_analysis'] = self._analyze_memory_file(files['memory'])
        
        # 分析性能文件
        if 'performance' in files:
            result['files_analyzed'].append(os.path.basename(files['performance']))
            result['performance_analysis'] = self._analyze_performance_file(files['performance'])
        
        return result
    
    def _analyze_cpu_file(self, file_path):
        """分析CPU文件"""
        try:
            cpu_data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if 'CpuUsage' in data and 'g_stageStability' in data:
                            cpu_data.append({
                                'timestamp': data.get('time', ''),
                                'cpu_usage': float(data['CpuUsage']),
                                'stage': int(data['g_stageStability'])
                            })
                    except:
                        continue
            
            if cpu_data:
                # 过滤稳定阶段数据 (g_stageStability=3)
                stable_data = [d for d in cpu_data if d['stage'] == 3]
                
                if stable_data:
                    cpu_values = [d['cpu_usage'] for d in stable_data]
                    return {
                        'total_samples': len(cpu_data),
                        'stable_samples': len(stable_data),
                        'avg_cpu': sum(cpu_values) / len(cpu_values),
                        'max_cpu': max(cpu_values),
                        'min_cpu': min(cpu_values),
                        'data_points': len(stable_data)
                    }
            
            return {'error': '没有找到有效的CPU数据'}
            
        except Exception as e:
            return {'error': f'CPU文件分析失败: {str(e)}'}
    
    def _analyze_memory_file(self, file_path):
        """分析内存文件"""
        try:
            memory_data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if 'TotalPss' in data and 'g_stageStability' in data:
                            memory_data.append({
                                'timestamp': data.get('time', ''),
                                'total_pss': float(data['TotalPss']),
                                'native_pss': float(data.get('NativePss', 0)),
                                'stage': int(data['g_stageStability'])
                            })
                    except:
                        continue
            
            if memory_data:
                # 过滤稳定阶段数据
                stable_data = [d for d in memory_data if d['stage'] == 3]
                
                if stable_data:
                    total_values = [d['total_pss'] for d in stable_data]
                    native_values = [d['native_pss'] for d in stable_data]
                    
                    return {
                        'total_samples': len(memory_data),
                        'stable_samples': len(stable_data),
                        'avg_total_pss': sum(total_values) / len(total_values),
                        'max_total_pss': max(total_values),
                        'avg_native_pss': sum(native_values) / len(native_values),
                        'max_native_pss': max(native_values),
                        'data_points': len(stable_data)
                    }
            
            return {'error': '没有找到有效的内存数据'}
            
        except Exception as e:
            return {'error': f'内存文件分析失败: {str(e)}'}
    
    def _analyze_performance_file(self, file_path):
        """分析性能文件"""
        try:
            performance_data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if 'DetectTime' in data:
                            performance_data.append({
                                'timestamp': data.get('time', ''),
                                'detect_time': float(data['DetectTime']),
                                'module': data.get('module', 'unknown')
                            })
                    except:
                        continue
            
            if performance_data:
                # 按模块分组统计
                modules = {}
                for d in performance_data:
                    module = d['module']
                    if module not in modules:
                        modules[module] = []
                    modules[module].append(d['detect_time'])
                
                result = {
                    'total_samples': len(performance_data),
                    'modules': {}
                }
                
                for module, times in modules.items():
                    result['modules'][module] = {
                        'count': len(times),
                        'avg_time': sum(times) / len(times),
                        'max_time': max(times),
                        'min_time': min(times)
                    }
                
                return result
            
            return {'error': '没有找到有效的性能数据'}
            
        except Exception as e:
            return {'error': f'性能文件分析失败: {str(e)}'}
