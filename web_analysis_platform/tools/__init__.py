#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具管理模块
"""

from .base_tool import BaseTool
from .c_analysis import CAnalysisTool
from .java_analysis import JavaAnalysisTool
from config import Config

class ToolManager:
    """工具管理器"""
    
    def __init__(self):
        self.tools = {}
        self._register_tools()
    
    def _register_tools(self):
        """注册所有工具"""
        # 注册C分析工具
        if 'c_analysis' in Config.TOOLS_CONFIG:
            self.tools['c_analysis'] = CAnalysisTool('c_analysis', Config.TOOLS_CONFIG['c_analysis'])
        
        # 注册Java分析工具
        if 'java_analysis' in Config.TOOLS_CONFIG:
            self.tools['java_analysis'] = JavaAnalysisTool('java_analysis', Config.TOOLS_CONFIG['java_analysis'])
    
    def get_tool(self, tool_id):
        """获取指定工具"""
        return self.tools.get(tool_id)
    
    def get_all_tools(self):
        """获取所有工具"""
        return list(self.tools.values())
    
    def register_tool(self, tool_id, tool_instance):
        """注册新工具"""
        if isinstance(tool_instance, BaseTool):
            self.tools[tool_id] = tool_instance
        else:
            raise ValueError("工具实例必须继承自BaseTool")
    
    def unregister_tool(self, tool_id):
        """注销工具"""
        if tool_id in self.tools:
            del self.tools[tool_id]
