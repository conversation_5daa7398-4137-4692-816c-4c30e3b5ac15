#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具基类
"""

import os
import subprocess
import json
from abc import ABC, abstractmethod
from datetime import datetime

class BaseTool(ABC):
    """分析工具基类"""
    
    def __init__(self, tool_id, config):
        self.tool_id = tool_id
        self.name = config.get('name', 'Unknown Tool')
        self.description = config.get('description', '')
        self.script_path = config.get('script_path', '')
        self.icon = config.get('icon', 'fas fa-tools')
        self.color = config.get('color', 'secondary')
        self.file_types = config.get('file_types', [])
        
        # 解析脚本的绝对路径
        if self.script_path and not os.path.isabs(self.script_path):
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.script_path = os.path.abspath(os.path.join(base_dir, self.script_path))
    
    def get_info(self):
        """获取工具信息"""
        return {
            'id': self.tool_id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'color': self.color,
            'file_types': self.file_types
        }
    
    @abstractmethod
    def run_analysis(self, input_dir, output_dir, params=None):
        """
        运行分析
        
        Args:
            input_dir: 输入文件目录
            output_dir: 输出结果目录
            params: 额外参数
            
        Returns:
            dict: 分析结果
        """
        pass
    
    def _run_script(self, script_args, cwd=None):
        """
        运行Python脚本
        
        Args:
            script_args: 脚本参数列表
            cwd: 工作目录
            
        Returns:
            tuple: (returncode, stdout, stderr)
        """
        try:
            if not os.path.exists(self.script_path):
                raise FileNotFoundError(f"脚本文件不存在: {self.script_path}")
            
            # 构建命令
            cmd = ['python', self.script_path] + script_args
            
            # 运行脚本
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                cwd=cwd
            )
            
            stdout, stderr = process.communicate(timeout=300)  # 5分钟超时
            
            return process.returncode, stdout, stderr
            
        except subprocess.TimeoutExpired:
            process.kill()
            raise Exception("脚本执行超时")
        except Exception as e:
            raise Exception(f"脚本执行失败: {str(e)}")
    
    def _prepare_files(self, input_dir):
        """
        准备输入文件
        
        Args:
            input_dir: 输入目录
            
        Returns:
            dict: 文件路径映射
        """
        files = {}
        
        if not os.path.exists(input_dir):
            return files
        
        # 遍历输入目录中的所有文件
        for filename in os.listdir(input_dir):
            filepath = os.path.join(input_dir, filename)
            if os.path.isfile(filepath):
                files[filename] = filepath
        
        return files
    
    def _save_result(self, output_dir, result_data, filename='analysis_result.json'):
        """
        保存分析结果
        
        Args:
            output_dir: 输出目录
            result_data: 结果数据
            filename: 文件名
        """
        os.makedirs(output_dir, exist_ok=True)
        
        result_file = os.path.join(output_dir, filename)
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    def _create_result_summary(self, success=True, message="", data=None, files=None):
        """
        创建结果摘要
        
        Args:
            success: 是否成功
            message: 消息
            data: 数据
            files: 生成的文件列表
            
        Returns:
            dict: 结果摘要
        """
        return {
            'tool_id': self.tool_id,
            'tool_name': self.name,
            'timestamp': datetime.now().isoformat(),
            'success': success,
            'message': message,
            'data': data or {},
            'files': files or []
        }
