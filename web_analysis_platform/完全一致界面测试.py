#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全一致界面功能测试
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_identical_interface():
    """测试完全一致的界面功能"""
    print("=" * 60)
    print("完全一致界面功能测试")
    print("=" * 60)
    
    # 测试应用导入
    try:
        from app import app
        print("✓ Flask应用导入成功")
    except Exception as e:
        print(f"✗ Flask应用导入失败: {e}")
        return False
    
    # 测试新增API路由
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append(rule.rule)
    
    expected_routes = [
        '/web_tool/<tool_id>',
        '/api/upload_single_file',
        '/api/upload_multiple_files',
        '/api/perform_analysis',
        '/api/perform_batch_analysis'
    ]
    
    print("\n检查新增API路由:")
    for route in expected_routes:
        route_exists = any(route.replace('<tool_id>', 'test') in r or route in r for r in routes)
        if route_exists:
            print(f"✓ {route}")
        else:
            print(f"✗ {route} 未找到")
    
    # 测试模板文件
    print("\n检查模板文件:")
    template_files = [
        'templates/web_tool.html',
        'templates/tool_interface.html'
    ]
    
    for template in template_files:
        if os.path.exists(template):
            print(f"✓ {template}")
        else:
            print(f"✗ {template}")
    
    # 测试分析函数
    print("\n检查分析函数:")
    try:
        from app import perform_single_analysis, perform_c_analysis, perform_java_analysis
        print("✓ 单个分析函数导入成功")
        print("✓ C分析函数导入成功")
        print("✓ Java分析函数导入成功")
    except Exception as e:
        print(f"✗ 分析函数导入失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ 完全一致界面功能测试完成")
    print("=" * 60)
    
    return True

def create_test_data():
    """创建测试数据"""
    print("\n创建测试数据:")
    
    test_data_dir = 'static/test_data'
    os.makedirs(test_data_dir, exist_ok=True)
    
    # C工具测试数据
    cpu_test_data = """# CPU使用率测试数据
2024-01-01_10:00:00 45.2
2024-01-01_10:00:01 47.8
2024-01-01_10:00:02 52.1
2024-01-01_10:00:03 48.5
2024-01-01_10:00:04 51.3
2024-01-01_10:00:05 49.7
2024-01-01_10:00:06 53.2
2024-01-01_10:00:07 46.9
2024-01-01_10:00:08 50.4
2024-01-01_10:00:09 48.1
"""
    
    memory_test_data = """# 内存使用量测试数据
2024-01-01_10:00:00 1024.5
2024-01-01_10:00:01 1056.2
2024-01-01_10:00:02 1089.7
2024-01-01_10:00:03 1045.8
2024-01-01_10:00:04 1078.3
2024-01-01_10:00:05 1092.1
2024-01-01_10:00:06 1067.4
2024-01-01_10:00:07 1034.9
2024-01-01_10:00:08 1051.6
2024-01-01_10:00:09 1073.8
"""
    
    performance_test_data = """# 性能检测测试数据
2024-01-01_10:00:00 15.6
2024-01-01_10:00:01 18.2
2024-01-01_10:00:02 16.8
2024-01-01_10:00:03 17.5
2024-01-01_10:00:04 19.1
2024-01-01_10:00:05 16.3
2024-01-01_10:00:06 18.7
2024-01-01_10:00:07 15.9
2024-01-01_10:00:08 17.2
2024-01-01_10:00:09 18.4
"""
    
    # Java工具测试数据
    java_cpu_memory_data = """# Java CPU/内存测试数据
2024-01-01_10:00:00 45.2 1024.5
2024-01-01_10:00:01 47.8 1056.2
2024-01-01_10:00:02 52.1 1089.7
2024-01-01_10:00:03 48.5 1045.8
2024-01-01_10:00:04 51.3 1078.3
2024-01-01_10:00:05 49.7 1092.1
2024-01-01_10:00:06 53.2 1067.4
2024-01-01_10:00:07 46.9 1034.9
2024-01-01_10:00:08 50.4 1051.6
2024-01-01_10:00:09 48.1 1073.8
"""
    
    java_performance_data = """# Java性能模块测试数据
ModuleA 125.6 success
ModuleB 89.3 success
ModuleC 156.8 success
ModuleA 132.1 success
ModuleB 91.7 success
ModuleC 149.2 success
ModuleA 128.4 success
ModuleB 87.9 success
ModuleC 152.3 success
ModuleA 130.7 success
"""
    
    java_encryption_data = """# Java加密检测测试数据
2024-01-01_10:00:00 AES success
2024-01-01_10:00:01 RSA success
2024-01-01_10:00:02 DES failed
2024-01-01_10:00:03 AES success
2024-01-01_10:00:04 RSA success
2024-01-01_10:00:05 AES success
2024-01-01_10:00:06 DES failed
2024-01-01_10:00:07 RSA success
2024-01-01_10:00:08 AES success
2024-01-01_10:00:09 RSA success
"""
    
    # 写入测试文件
    test_files = {
        'stability_CPU_test.txt': cpu_test_data,
        'stability_Memory_test.txt': memory_test_data,
        'stability_Detect_test.txt': performance_test_data,
        'java_cpu_memory_test.txt': java_cpu_memory_data,
        'java_performance_test.txt': java_performance_data,
        'java_encryption_test.txt': java_encryption_data
    }
    
    for filename, content in test_files.items():
        filepath = os.path.join(test_data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 创建测试文件: {filename}")
    
    print(f"\n测试数据文件已创建在: {test_data_dir}")

def main():
    """主函数"""
    success = test_identical_interface()
    create_test_data()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 完全一致界面功能集成成功！")
        print("\n界面特点:")
        print("✓ 完全模拟tkinter界面样式")
        print("✓ 标签页切换功能")
        print("✓ 文件浏览和上传")
        print("✓ 单个分析和批量分析")
        print("✓ 结果显示和图表展示")
        print("✓ 清除结果功能")
        
        print("\n使用方法:")
        print("1. 运行: python app.py")
        print("2. 访问: http://localhost:5000")
        print("3. 登录后选择C或Java工具")
        print("4. 点击'网页版启动（推荐）'")
        print("5. 使用界面与原始工具完全一致")
        
        print("\n测试步骤:")
        print("1. 选择对应的标签页（CPU分析、内存分析等）")
        print("2. 点击'浏览'按钮选择测试数据文件")
        print("3. 点击对应的分析按钮")
        print("4. 查看分析结果和图表")
        print("5. 使用'清除结果'按钮清空结果")
        
    else:
        print("❌ 完全一致界面功能集成存在问题，请检查配置")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
