# 分析工具平台使用说明

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）
1. 双击 `start.bat` 文件
2. 等待自动安装依赖和启动服务
3. 浏览器会自动打开 http://localhost:5000

### 方法二：手动启动
1. 打开命令行，进入项目目录
2. 安装依赖：`pip install -r requirements.txt`
3. 启动应用：`python run.py`
4. 打开浏览器访问：http://localhost:5000

## 🔐 登录信息
- **用户名**：`lcj`
- **密码**：`123`

## 📋 使用流程

### 1. 登录系统
- 打开浏览器访问 http://localhost:5000
- 输入用户名和密码登录

### 2. 选择分析工具
在仪表板中可以看到两个工具：
- **C标准化分析工具**：点击直接启动原始的C标准分析工具GUI
- **Java标准化分析工具**：点击直接启动原始的Java标准化分析工具GUI

### 3. 启动工具
- 点击工具卡片上的"启动GUI工具"按钮
- 系统会自动启动对应的原始分析工具
- 工具会在新的窗口中打开，保持原有的所有功能

### 4. 使用原始工具
- 启动后就是您熟悉的原始工具界面
- 所有功能保持不变，包括文件选择、分析、结果展示等
- 可以同时启动多个工具实例

## 📁 支持的文件格式

### C标准化分析工具
- `stability_CPU_*.txt` - CPU数据文件
- `stability_Memory_*.txt` - 内存数据文件  
- `stability_Detect_*.txt` - 性能检测文件

### Java标准化分析工具
- CPU/内存数据文件（JSON格式）
- 性能数据文件（JSON格式）
- 模型加密日志文件（TXT/LOG格式）

## 🔧 功能特性

### 文件上传
- ✅ 支持拖拽上传
- ✅ 支持批量文件上传
- ✅ 自动文件类型识别
- ✅ 上传进度显示

### 分析处理
- ✅ 后台异步处理
- ✅ 实时状态更新
- ✅ 错误处理和提示
- ✅ 分析结果缓存

### 结果展示
- ✅ 结果摘要显示
- ✅ 详细数据展示
- ✅ 文件下载功能
- ✅ JSON格式化显示

### 用户界面
- ✅ 响应式设计
- ✅ 现代化UI
- ✅ 中文界面
- ✅ 操作提示

## ⚠️ 注意事项

1. **原始脚本保护**
   - 本平台不会修改您的原始分析脚本
   - 只是通过包装器调用原始脚本功能

2. **文件安全**
   - 上传的文件存储在临时目录
   - 建议定期清理上传和结果目录

3. **浏览器兼容性**
   - 推荐使用Chrome、Firefox、Safari或Edge
   - 需要启用JavaScript

4. **网络访问**
   - 默认只能本机访问（localhost）
   - 如需局域网访问，请修改配置

## 🛠️ 故障排除

### 无法启动应用
1. 检查Python版本（需要3.7+）
2. 检查是否安装了所需依赖
3. 检查端口5000是否被占用

### 文件上传失败
1. 检查文件格式是否正确
2. 检查文件大小（限制100MB）
3. 检查磁盘空间是否充足

### 分析失败
1. 检查原始脚本是否存在
2. 检查文件内容格式是否正确
3. 查看错误提示信息

### 无法访问网页
1. 确认服务器已启动
2. 检查防火墙设置
3. 尝试使用127.0.0.1:5000

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台错误信息
2. 浏览器开发者工具
3. 服务器日志输出

## 🔄 更新日志

### v1.0.0
- ✅ 基础Web平台框架
- ✅ 用户登录认证
- ✅ C标准化分析工具集成
- ✅ Java标准化分析工具集成
- ✅ 文件上传和下载
- ✅ 响应式UI设计
