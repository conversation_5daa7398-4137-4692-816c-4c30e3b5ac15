@echo off
chcp 65001 >nul
title Java标准化分析工具

echo ============================================================
echo              Java标准化分析工具启动器
echo ============================================================
echo.

if not exist "Java标准化分析工具UI.py" (
    echo ❌ 错误: 找不到 Java标准化分析工具UI.py 文件
    echo 请确保此批处理文件与脚本文件在同一目录下
    pause
    exit /b 1
)

echo 🚀 正在启动 Java标准化分析工具...
echo.

start "Java标准化分析工具" python "Java标准化分析工具UI.py"

if errorlevel 1 (
    echo ❌ 启动失败，请检查Python环境
    pause
) else (
    echo ✅ Java标准化分析工具已启动！
    echo 工具窗口应该已经打开
    echo.
    echo 您可以关闭此窗口
    timeout /t 3 >nul
)
