分析工具启动器 - 使用说明
========================================

📁 文件说明
========================================

1. 工具启动器.bat          - 主启动器（推荐使用）
2. 启动C工具.bat           - 直接启动C标准化分析工具
3. 启动Java工具.bat        - 直接启动Java标准化分析工具
4. 启动工具.py             - Python命令行启动器
5. C标准分析工具UI.py      - 您的C分析工具脚本
6. Java标准化分析工具UI.py - 您的Java分析工具脚本

🚀 使用方法
========================================

方法一：使用主启动器（推荐）
1. 双击 "工具启动器.bat"
2. 在菜单中选择要启动的工具
3. 工具会在新窗口中打开

方法二：直接启动单个工具
1. 双击 "启动C工具.bat" 启动C工具
2. 双击 "启动Java工具.bat" 启动Java工具

方法三：使用Python启动器
1. 双击 "启动工具.py" 或在命令行运行
2. 按提示选择要启动的工具

✅ 功能特点
========================================

- 🎯 一键启动：点击即可启动对应工具
- 🔄 多实例：支持同时启动多个工具实例
- 💻 独立窗口：每个工具在独立窗口中运行
- 🛡️ 错误检查：自动检查文件是否存在
- 📋 菜单选择：友好的菜单界面

⚠️ 注意事项
========================================

1. 确保所有文件在同一目录下
2. 确保已安装Python环境
3. 确保原始脚本文件存在且可运行
4. 如果启动失败，请检查Python环境配置

🔧 故障排除
========================================

问题：双击批处理文件没有反应
解决：右键选择"以管理员身份运行"

问题：提示找不到Python
解决：确保Python已安装并添加到系统PATH

问题：提示找不到脚本文件
解决：确保脚本文件与启动器在同一目录

问题：工具启动后立即关闭
解决：检查原始脚本是否有错误

📞 技术支持
========================================

如果遇到问题，请检查：
1. Python版本是否兼容
2. 脚本文件是否完整
3. 依赖库是否已安装
4. 文件路径是否正确

========================================
版本：1.0
更新日期：2025-07-18
========================================
