#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Python脚本打包成exe程序
"""

import os
import subprocess
import sys
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ PyInstaller安装失败")
        return False

def build_exe(script_name, exe_name):
    """打包单个脚本为exe"""
    if not os.path.exists(script_name):
        print(f"✗ 脚本文件不存在: {script_name}")
        return False
    
    print(f"正在打包 {script_name} -> {exe_name}.exe")
    
    try:
        # PyInstaller命令
        cmd = [
            'pyinstaller',
            '--onefile',                    # 打包成单个exe文件
            '--windowed',                   # 不显示控制台窗口
            '--name', exe_name,             # 指定exe文件名
            '--distpath', 'exe_programs',   # 输出目录
            '--workpath', 'build_temp',     # 临时工作目录
            '--specpath', 'build_temp',     # spec文件目录
            script_name
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {exe_name}.exe 打包成功")
            return True
        else:
            print(f"✗ 打包失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("✗ PyInstaller未找到，请先安装")
        return False
    except Exception as e:
        print(f"✗ 打包过程出错: {e}")
        return False

def cleanup_build_files():
    """清理打包过程产生的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_remove = ['build_temp', '__pycache__']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除目录: {dir_name}")
            except Exception as e:
                print(f"✗ 删除目录失败 {dir_name}: {e}")
    
    # 删除.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                    print(f"✓ 删除文件: {os.path.join(root, file)}")
                except:
                    pass

def main():
    """主函数"""
    print("=" * 60)
    print("Python脚本打包工具")
    print("=" * 60)
    
    # 要打包的脚本
    scripts = [
        ('C标准分析工具UI.py', 'C标准分析工具'),
        ('Java标准化分析工具UI.py', 'Java标准化分析工具')
    ]
    
    # 检查脚本是否存在
    missing_scripts = []
    for script_name, exe_name in scripts:
        if not os.path.exists(script_name):
            missing_scripts.append(script_name)
    
    if missing_scripts:
        print("以下脚本文件不存在:")
        for script in missing_scripts:
            print(f"  ✗ {script}")
        print("\n请确保脚本文件在当前目录下")
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 创建输出目录
    os.makedirs('exe_programs', exist_ok=True)
    
    # 打包脚本
    success_count = 0
    for script_name, exe_name in scripts:
        if build_exe(script_name, exe_name):
            success_count += 1
    
    # 清理临时文件
    cleanup_build_files()
    
    print("\n" + "=" * 60)
    print(f"打包完成！成功: {success_count}/{len(scripts)}")
    
    if success_count > 0:
        print(f"\nEXE程序位置: exe_programs/")
        print("生成的文件:")
        for script_name, exe_name in scripts:
            exe_path = f"exe_programs/{exe_name}.exe"
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"  ✓ {exe_name}.exe ({size_mb:.1f} MB)")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
