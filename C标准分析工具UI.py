# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C标准分析工具UI
统一的可视化界面，集成CPU、内存和性能分析功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from datetime import datetime
import statistics
import matplotlib.font_manager as fm
import os
import sys
import threading
from io import BytesIO
from PIL import Image

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class CStandardAnalysisToolUI:
    def __init__(self, root):
        self.root = root
        self.root.title("C标准分析工具")
        self.root.geometry("1200x800")

        # 添加窗口关闭事件绑定
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # 创建主框架
        self.create_widgets()

        # 存储图表对象，用于复制功能
        self.cpu_figure = None
        self.memory_figure = None
        self.performance_figure = None

    def create_widgets(self):
        """创建UI组件"""
        # 创建统一的目录选择区域
        dir_frame = ttk.LabelFrame(self.root, text="统一目录选择", padding=10)
        dir_frame.pack(fill=tk.X, padx=10, pady=5)

        self.result_dir_var = tk.StringVar()
        ttk.Label(dir_frame, text="选择结果目录:").pack(anchor=tk.W)
        dir_entry_frame = ttk.Frame(dir_frame)
        dir_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(dir_entry_frame, textvariable=self.result_dir_var, width=80).pack(side=tk.LEFT, fill=tk.X,
                                                                                    expand=True)
        ttk.Button(dir_entry_frame, text="浏览", command=self.browse_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # 批量分析按钮
        ttk.Button(dir_frame, text="批量分析", command=self.batch_analyze).pack(side=tk.LEFT, padx=5, pady=5)

        # 分隔线
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill=tk.X, padx=10, pady=5)

        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建各个功能标签页
        self.create_cpu_tab()
        self.create_memory_tab()
        self.create_performance_tab()

    def create_cpu_tab(self):
        """创建CPU分析标签页"""
        cpu_frame = ttk.Frame(self.notebook)
        self.notebook.add(cpu_frame, text="CPU分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(cpu_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.cpu_file_var = tk.StringVar()
        ttk.Label(file_frame, text="CPU数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.cpu_file_var, width=80).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_cpu_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 控制按钮
        control_frame = ttk.Frame(cpu_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析CPU数据", command=self.analyze_cpu).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_cpu_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_cpu_chart).pack(side=tk.LEFT, padx=5)

        # 图表区域
        self.cpu_chart_frame = ttk.LabelFrame(cpu_frame, text="CPU使用率图表", padding=10)
        self.cpu_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(cpu_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)

        self.cpu_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.cpu_result_text.pack(fill=tk.BOTH, expand=True)

    def create_memory_tab(self):
        """创建内存分析标签页"""
        memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(memory_frame, text="内存分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(memory_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.memory_file_var = tk.StringVar()
        ttk.Label(file_frame, text="内存数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.memory_file_var, width=80).pack(side=tk.LEFT, fill=tk.X,
                                                                                      expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_memory_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 控制按钮
        control_frame = ttk.Frame(memory_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析内存数据", command=self.analyze_memory).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_memory_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_memory_chart).pack(side=tk.LEFT, padx=5)

        # 图表区域
        self.memory_chart_frame = ttk.LabelFrame(memory_frame, text="内存使用趋势图", padding=10)
        self.memory_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(memory_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)

        self.memory_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.memory_result_text.pack(fill=tk.BOTH, expand=True)

    def create_performance_tab(self):
        """创建性能分析标签页"""
        performance_frame = ttk.Frame(self.notebook)
        self.notebook.add(performance_frame, text="性能分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(performance_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        self.performance_file_var = tk.StringVar()
        ttk.Label(file_frame, text="性能数据文件:").pack(anchor=tk.W)
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(file_entry_frame, textvariable=self.performance_file_var, width=80).pack(side=tk.LEFT, fill=tk.X,
                                                                                           expand=True)
        ttk.Button(file_entry_frame, text="浏览", command=self.browse_performance_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 控制按钮
        control_frame = ttk.Frame(performance_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(control_frame, text="分析性能数据", command=self.analyze_performance).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="复制图表", command=self.copy_performance_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除图表", command=self.clear_performance_chart).pack(side=tk.LEFT, padx=5)

        # 图表区域
        self.performance_chart_frame = ttk.LabelFrame(performance_frame, text="性能趋势图", padding=10)
        self.performance_chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(performance_frame, text="分析结果", padding=10)
        result_frame.pack(fill=tk.X, padx=10, pady=5)

        self.performance_result_text = scrolledtext.ScrolledText(result_frame, height=6, wrap=tk.WORD)
        self.performance_result_text.pack(fill=tk.BOTH, expand=True)

    # 文件浏览方法
    def browse_cpu_file(self):
        filename = filedialog.askopenfilename(
            title="选择CPU数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.cpu_file_var.set(filename)

    def browse_memory_file(self):
        filename = filedialog.askopenfilename(
            title="选择内存数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.memory_file_var.set(filename)

    def browse_performance_file(self):
        filename = filedialog.askopenfilename(
            title="选择性能数据文件",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.performance_file_var.set(filename)

    def browse_directory(self):
        """浏览并选择结果目录"""
        directory = filedialog.askdirectory(title="选择包含结果文件的目录")
        if directory:
            self.result_dir_var.set(directory)
            self.auto_fill_files(directory)

    def auto_fill_files(self, directory):
        """自动识别目录下的三类结果文件"""
        import os
        import re

        cpu_pattern = re.compile(r'stability_CPU_\d{8}_\d{6}\.txt')
        detect_pattern = re.compile(r'stability_Detect_\d{8}_\d{6}\.txt')
        memory_pattern = re.compile(r'stability_Memory_\d{8}_\d{6}\.txt')

        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if cpu_pattern.match(filename):
                self.cpu_file_var.set(filepath)
            elif detect_pattern.match(filename):
                self.performance_file_var.set(filepath)
            elif memory_pattern.match(filename):
                self.memory_file_var.set(filepath)

    # 图表复制功能
    def copy_figure_to_clipboard(self, figure):
        """将matplotlib图表复制到剪贴板"""
        try:
            # 尝试使用win32clipboard
            try:
                import win32clipboard

                # 将图表保存到内存中的字节流
                buf = BytesIO()
                figure.savefig(buf, format='png', dpi=150, bbox_inches='tight',
                               facecolor='white', edgecolor='none')
                buf.seek(0)

                # 使用PIL打开图像
                img = Image.open(buf)

                # 将图像复制到Windows剪贴板
                output = BytesIO()
                img.convert('RGB').save(output, 'BMP')
                data = output.getvalue()[14:]  # BMP文件头是14字节
                output.close()
                buf.close()

                # 复制到Windows剪贴板
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                win32clipboard.CloseClipboard()

                messagebox.showinfo("成功", "图表已复制到剪贴板！")

            except ImportError:
                # 如果没有win32clipboard，使用PowerShell方法
                self.copy_figure_powershell(figure)

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def copy_figure_powershell(self, figure):
        """使用PowerShell复制图表"""
        try:
            import tempfile
            import subprocess

            # 保存图表到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            figure.savefig(temp_file.name, format='png', dpi=150, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
            temp_file.close()

            # 使用PowerShell复制图片到剪贴板
            cmd = f'powershell.exe "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.Clipboard]::SetImage([System.Drawing.Image]::FromFile(\'{temp_file.name}\'))"'
            subprocess.run(cmd, shell=True, check=True)

            # 删除临时文件
            os.unlink(temp_file.name)

            messagebox.showinfo("成功", "图表已复制到剪贴板！")

        except Exception as e:
            messagebox.showerror("错误", f"复制图表失败: {str(e)}")

    def create_chart_context_menu(self, canvas, figure):
        """为图表创建右键菜单"""
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="复制图表", command=lambda: self.copy_figure_to_clipboard(figure))
        context_menu.add_command(label="保存图表", command=lambda: self.save_figure_to_file(figure))

        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        canvas.get_tk_widget().bind("<Button-3>", show_context_menu)  # 右键
        return context_menu

    def save_figure_to_file(self, figure):
        """保存图表到文件"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"C标准图表_{current_time}.png"

            filename = filedialog.asksaveasfilename(
                title="保存图表到文件",
                initialname=default_filename,
                defaultextension=".png",
                filetypes=[
                    ("PNG图片", "*.png"),
                    ("PDF文档", "*.pdf"),
                    ("SVG矢量图", "*.svg"),
                    ("JPEG图片", "*.jpg"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                figure.savefig(filename, dpi=300, bbox_inches='tight',
                               facecolor='white', edgecolor='none')
                messagebox.showinfo("保存成功", f"图表已保存到:\n{filename}")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存图表失败: {str(e)}")

    # CPU分析方法
    def analyze_cpu(self):
        """分析CPU数据"""
        file_path = self.cpu_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择CPU数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_cpu_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_cpu_thread(self, file_path):
        """CPU分析线程"""
        try:
            times = []
            cpu_usages = []
            filtered_cpu_usages = []  # 仅用于计算g_stageStability=3的数据

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        current_cpu = data.get("current_CPU")
                        time_str = data.get("time", "")
                        g_stageStability = data.get("g_stageStability")
                        if current_cpu is not None and time_str:
                            cpu = current_cpu  # 直接使用原始值，不乘以100
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            cpu_usages.append(cpu)
                            if g_stageStability == 3:
                                filtered_cpu_usages.append(cpu)
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的CPU数据"))
                return

            # 计算统计信息（仅使用g_stageStability=3的数据）
            if filtered_cpu_usages:
                avg_cpu = statistics.mean(filtered_cpu_usages)
                max_cpu = max(filtered_cpu_usages)
                min_cpu = min(filtered_cpu_usages)
            else:
                avg_cpu = max_cpu = min_cpu = 0

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_cpu_ui(times, cpu_usages, avg_cpu, max_cpu, min_cpu,
                                                           filtered_cpu_usages))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_cpu_ui(self, times, cpu_usages, avg_cpu, max_cpu, min_cpu, filtered_cpu_usages):
        """更新CPU分析UI"""
        # 清除之前的图表
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        # ax.plot(times, cpu_usages, marker='o', linestyle='-', color='red', label='CPU 使用率 (%)')
        # ax.axhline(avg_cpu, color='orange', linestyle='--', alpha=0.8, label=f'g_stageStability=3时平均CPU使用率: {avg_cpu:.2f}%')
        ax.plot(times, cpu_usages, marker='o', linestyle='-', color='red', label='CPU 使用率 (%)')
        ax.axhline(avg_cpu, color='orange', linestyle='--', alpha=0.8)

        # 添加底部注释文字，并加框美化
        ax.text(
            0.5, -0.2,
            f"g_stageStability=3 时平均CPU使用率: {avg_cpu:.2f}%",
            transform=ax.transAxes,
            ha='center', va='top',
            fontsize=12, color='red',
            bbox=dict(
                boxstyle="round,pad=0.4",  # 圆角框+内边距
                facecolor='lightyellow',  # 背景色
                edgecolor='red',  # 边框颜色
                linewidth=1.5
            )
        )

        ax.set_xlabel("时间")
        ax.set_ylabel("使用率 (%)")
        ax.set_title("CPU 使用随时间变化")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        # 保存图表对象
        self.cpu_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.cpu_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""CPU分析结果：\n数据点数量: {len(cpu_usages)}（全部g_stageStability值）\n用于统计的数据点数: {len(filtered_cpu_usages)}（仅g_stageStability=3）\n\n平均CPU使用率: {avg_cpu:.2f}%  最大CPU使用率: {max_cpu:.2f}%  最小CPU使用率: {min_cpu:.2f}%\n分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}"""

        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_result_text.insert(tk.END, result_text)

    def copy_cpu_chart(self):
        """复制CPU图表"""
        if self.cpu_figure:
            self.copy_figure_to_clipboard(self.cpu_figure)
        else:
            messagebox.showwarning("警告", "请先分析CPU数据生成图表")

    def clear_cpu_chart(self):
        """清除CPU图表"""
        for widget in self.cpu_chart_frame.winfo_children():
            widget.destroy()
        self.cpu_result_text.delete(1.0, tk.END)
        self.cpu_figure = None

    # 内存分析方法
    def analyze_memory(self):
        """分析内存数据"""
        file_path = self.memory_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择内存数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_memory_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_memory_thread(self, file_path):
        """内存分析线程"""
        try:
            times = []
            memory_values = []
            filtered_memory_values = []  # 仅用于计算g_stageStability=3的数据
            stability_0_mem = []  # 用于存储g_stageStability=0的数据
            stability_2_mem = []  # 用于存储g_stageStability=2的数据
            stability_3_mem = []  # 用于存储g_stageStability=3的数据

            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        time_str = data.get("time")
                        mem_mb = data.get("current_memory")  # MB单位
                        g_stageStability = data.get("g_stageStability")
                        if time_str and mem_mb is not None:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            times.append(time_obj)
                            memory_values.append(mem_mb)

                            # 按g_stageStability分类存储数据
                            if g_stageStability == 0:
                                stability_0_mem.append(mem_mb)
                            elif g_stageStability == 2:
                                stability_2_mem.append(mem_mb)
                            elif g_stageStability == 3:
                                stability_3_mem.append(mem_mb)
                    except Exception:
                        continue

            if not times:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的内存数据"))
                return

            # 计算统计信息（仅使用g_stageStability=3的数据）
            if stability_3_mem:
                avg_mem = statistics.mean(stability_3_mem)
                max_mem = max(stability_3_mem)
                min_mem = min(stability_3_mem)
            else:
                avg_mem = max_mem = min_mem = 0

            # 计算新增指标
            mem_detect = max_mem  # 检测时整个testbed占用内存为g_stageStability=3时的最大值

            # 计算testbed空载时占用内存（取g_stageStability=0时的平均值）
            if stability_0_mem:
                mem_idle = statistics.mean(stability_0_mem)
            else:
                mem_idle = 0

            # 计算初始化内存占用（g_stageStability=2的平均值减去g_stageStability=0的平均值）
            if stability_2_mem:
                mem_init = max(0, statistics.mean(stability_2_mem) - mem_idle)  # 添加防止负值的保护
            else:
                mem_init = 0

            # 在主线程中更新UI
            self.root.after(0, lambda: self._update_memory_ui(times, memory_values, avg_mem, max_mem, min_mem,
                                                              stability_3_mem, mem_detect, mem_idle, mem_init))  # 传递新参数

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_memory_ui(self, times, memory_values, avg_mem, max_mem, min_mem, filtered_memory_values,
                          mem_detect=None, mem_idle=None, mem_init=None):
        """更新内存分析UI"""
        # 清除之前的图表
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        # ax.plot(times, memory_values, marker='o', linestyle='-', color='blue', label='内存占用 (MB)')
        # ax.axhline(avg_mem, color='orange', linestyle='--', label=f'平均内存占用: {avg_mem:.2f} MB')

        # 1. 内存折线图
        ax.plot(times, memory_values, marker='o', linestyle='-', color='blue', label='内存占用 (MB)')

        # 2. 平均值虚线
        ax.axhline(avg_mem, color='orange', linestyle='--')

        # 3. 底部注释文字 + 边框强调
        ax.text(
            0.05, -0.20,  # 根据前面 CPU 那个 -0.2，往下再挪一点防止重叠
            f"g_stageStability=3 时平均内存占用: {avg_mem:.2f} MB",
            transform=ax.transAxes,
            ha='center', va='top',
            fontsize=10, color='blue',
            bbox=dict(
                boxstyle="round,pad=0.4",
                facecolor='lightcyan',
                edgecolor='blue',
                linewidth=1.5
            )
        )

        # 新增内存分析指标注释（按规范要求垂直排列，Y坐标间隔0.05）
        ax.text(
            0.4, -0.20,  # 在原有基础上往下移0.05
            f"检测时整个testbed占用内存: {mem_detect:.2f} MB",
            transform=ax.transAxes,
            ha='center', va='top',
            fontsize=10, color='blue',
            bbox=dict(
                boxstyle="round,pad=0.4",
                facecolor='lightcyan',
                edgecolor='blue',
                linewidth=1.5
            )
        )

        ax.text(
            0.7, -0.20,  # 在原有基础上往下移0.10
            f"testbed空载时占用内存: {mem_idle:.2f} MB",
            transform=ax.transAxes,
            ha='center', va='top',
            fontsize=10, color='blue',
            bbox=dict(
                boxstyle="round,pad=0.4",
                facecolor='lightcyan',
                edgecolor='blue',
                linewidth=1.5
            )
        )

        ax.text(
            1.0, -0.20,  # 在原有基础上往下移0.15
            f"初始化内存占用: {max(0, mem_init):.2f} MB",  # 添加防止负值的保护
            transform=ax.transAxes,
            ha='center', va='top',
            fontsize=10, color='blue',
            bbox=dict(
                boxstyle="round,pad=0.4",
                facecolor='lightcyan',
                edgecolor='blue',
                linewidth=1.5
            )
        )

        ax.set_xlabel("时间")
        ax.set_ylabel("内存占用 (MB)")
        ax.set_title("内存占用随时间变化")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        # 保存图表对象
        self.memory_figure = fig

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.memory_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加右键菜单
        self.create_chart_context_menu(canvas, fig)

        # 更新结果文本
        result_text = f"""内存分析结果：\n数据点数量: {len(memory_values)}（全部g_stageStability值）\n用于统计的数据点数: {len(filtered_memory_values)}（仅g_stageStability=3）\n\n平均内存占用: {avg_mem:.2f} MB  最大内存占用: {max_mem:.2f} MB  最小内存占用: {min_mem:.2f} MB"""

        # 添加新增的内存分析指标到结果文本
        if mem_detect is not None:
            result_text += f"\n\n检测时整个testbed占用内存: {mem_detect:.2f} MB"
        if mem_idle is not None:
            result_text += f"\ntestbed空载时占用内存: {mem_idle:.2f} MB"
        if mem_init is not None:
            result_text += f"\n初始化内存占用: {max(0, mem_init):.2f} MB"

        result_text += f"\n\n分析时间范围: {times[0].strftime('%Y-%m-%d %H:%M:%S')} 到 {times[-1].strftime('%Y-%m-%d %H:%M:%S')}"

        self.memory_result_text.delete(1.0, tk.END)
        self.memory_result_text.insert(tk.END, result_text)

    def copy_memory_chart(self):
        """复制内存图表"""
        if self.memory_figure:
            self.copy_figure_to_clipboard(self.memory_figure)
        else:
            messagebox.showwarning("警告", "请先分析内存数据生成图表")

    def clear_memory_chart(self):
        """清除内存图表"""
        for widget in self.memory_chart_frame.winfo_children():
            widget.destroy()
        self.memory_result_text.delete(1.0, tk.END)
        self.memory_figure = None

    # 性能分析方法
    def analyze_performance(self):
        """分析性能数据"""
        file_path = self.performance_file_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择性能数据文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", "文件不存在")
            return

        try:
            threading.Thread(target=self._analyze_performance_thread, args=(file_path,), daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {str(e)}")

    def _analyze_performance_thread(self, file_path):
        """性能分析线程（多模块支持）"""
        try:
            module_data = {}
            filtered_module_data = {}  # 仅用于计算g_stageStability=3的数据

            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取所有行
                lines = f.readlines()

                # 跳过前5行，从第6行开始处理
                for line in lines[5:]:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        data = json.loads(line)
                        module = data.get("module", "Unknown")
                        cost_time = data.get("costTime")
                        time_str = data.get("time")
                        g_stageStability = data.get("g_stageStability")
                        if cost_time is not None and time_str:
                            time_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                            # 所有数据都加入module_data用于绘图
                            if module not in module_data:
                                module_data[module] = {"times": [], "costs": []}
                            module_data[module]["times"].append(time_obj)
                            module_data[module]["costs"].append(cost_time)
                            # 仅g_stageStability=3的数据加入filtered_module_data用于统计
                            if g_stageStability == 3:
                                if module not in filtered_module_data:
                                    filtered_module_data[module] = []
                                filtered_module_data[module].append(cost_time)
                    except Exception:
                        continue

            if not module_data:
                self.root.after(0, lambda: messagebox.showerror("错误", "没有有效的性能数据"))
                return

            self.root.after(0, lambda: self._update_performance_ui_multimodule(module_data, filtered_module_data))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def _update_performance_ui_multimodule(self, module_data, filtered_module_data):
        """更新支持多模块的性能分析UI"""
        for widget in self.performance_chart_frame.winfo_children():
            widget.destroy()

        fig, ax = plt.subplots(figsize=(12, 6))
        result_text = "=== 多模块性能分析结果 ===\n\n"

        all_costs = []  # 用于计算整体平均值
        colors = ['green', 'blue', 'red', 'purple', 'orange']
        for idx, (module, data) in enumerate(module_data.items()):
            times = data["times"]
            costs = data["costs"]
            if not times:
                continue

            # 使用过滤后的数据计算统计值（仅g_stageStability=3）
            filtered_costs = filtered_module_data.get(module, [])
            if filtered_costs:
                avg_cost = statistics.mean(filtered_costs)
                max_cost = max(filtered_costs)
                min_cost = min(filtered_costs)
                count = len(filtered_costs)
                all_costs.extend(filtered_costs)  # 只将过滤后的数据加入整体统计
            else:
                # 如果没有过滤数据，则使用全部数据计算
                avg_cost = statistics.mean(costs) if costs else 0
                max_cost = max(costs) if costs else 0
                min_cost = min(costs) if costs else 0
                count = len(costs)
                all_costs.extend(costs)  # 将全部数据加入整体统计

            ax.plot(times, costs, marker='o', linestyle='-', color=colors[idx % len(colors)], label=f"{module}")

            # 在图表中绘制该模块的平均线和数值（右上角）
            ax.axhline(avg_cost, color=colors[idx % len(colors)], linestyle='--', linewidth=1, alpha=0.6)
            # ax.annotate(f"{module} 平均: {avg_cost:.2f} ms",
            #             xy=(times[-1], avg_cost),
            #             xytext=(10, -10),
            #             textcoords='offset points',
            #             color=colors[idx % len(colors)],
            #             fontsize=9,
            #             bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.5),
            #             arrowprops=dict(arrowstyle='->', color=colors[idx % len(colors)]))
            # 在图表底部添加模块平均值说明（不用箭头）
            ax.text(
                0.5 - 0.2 * idx, -0.2,  # 纵向逐行下移，防止重叠（按 idx 变化）
                f"{module} 平均: {avg_cost:.2f} ms",
                transform=ax.transAxes,
                ha='center', va='top',
                fontsize=10,
                color=colors[idx % len(colors)],
                bbox=dict(
                    boxstyle='round,pad=0.4',
                    facecolor='lightyellow',
                    edgecolor=colors[idx % len(colors)],
                    linewidth=1.2,
                    alpha=0.85
                )
            )

            result_text += f"模块: {module}\n"
            result_text += f"  数据点数: {len(costs)}（全部g_stageStability值）\n"
            result_text += f"  用于统计的数据点数: {count}（仅g_stageStability=3）\n"
            result_text += f"\n平均处理时间: {avg_cost:.2f} ms  最大处理时间: {max_cost:.2f} ms  最小处理时间: {min_cost:.2f} ms\n\n"

        # 添加总体平均值显示
        if all_costs:
            overall_avg = statistics.mean(all_costs)
            result_text += f"---\n整体平均处理时间: {overall_avg:.2f} ms\n"
            ax.axhline(overall_avg, color='gray', linestyle='--', linewidth=1)

        ax.set_xlabel("时间")
        ax.set_ylabel("处理时间 (毫秒)")
        ax.set_title("不同模块 costTime 随时间变化")
        ax.legend()
        ax.grid(True)
        fig.autofmt_xdate()

        self.performance_figure = fig

        canvas = FigureCanvasTkAgg(fig, self.performance_chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.create_chart_context_menu(canvas, fig)

        self.performance_result_text.delete(1.0, tk.END)
        self.performance_result_text.insert(tk.END, result_text)

    def copy_performance_chart(self):
        """复制性能图表"""
        if self.performance_figure:
            self.copy_figure_to_clipboard(self.performance_figure)
        else:
            messagebox.showwarning("警告", "请先分析性能数据生成图表")

    def clear_performance_chart(self):
        """清除性能图表"""
        for widget in self.performance_chart_frame.winfo_children():
            widget.destroy()
        self.performance_result_text.delete(1.0, tk.END)
        self.performance_figure = None

    def batch_analyze(self):
        """执行批量分析"""
        dir_path = self.result_dir_var.get()
        if not dir_path or not os.path.exists(dir_path):
            # 如果目录路径无效，尝试将路径作为文件路径直接处理
            # 检查是否找到所有三类文件
            found_files = {
                'cpu': self.cpu_file_var.get(),
                'memory': self.memory_file_var.get(),
                'performance': self.performance_file_var.get()
            }

            missing = [key for key, path in found_files.items() if not path or not os.path.exists(path)]
            if missing:
                messagebox.showwarning("警告", f"未找到以下类型的文件: {', '.join(missing)}")
                return

            # 启动分析线程
            try:
                # 启动分析线程
                threading.Thread(
                    target=self._batch_analysis_thread,
                    args=(found_files,),
                    daemon=True
                ).start()
            except Exception as e:
                messagebox.showerror("错误", f"启动分析失败: {str(e)}")
            return

        # 如果是有效目录，使用原有逻辑自动填充文件
        # 增加强制刷新文件路径逻辑
        self.auto_fill_files(dir_path)  # 强制刷新文件路径

        # 检查是否找到所有三类文件
        found_files = {
            'cpu': self.cpu_file_var.get(),
            'memory': self.memory_file_var.get(),
            'performance': self.performance_file_var.get()
        }

        missing = [key for key, path in found_files.items() if not path or not os.path.exists(path)]
        if missing:
            messagebox.showwarning("警告", f"未找到以下类型的文件: {', '.join(missing)}")
            return

        # 启动分析线程
        try:
            # 启动分析线程
            threading.Thread(
                target=self._batch_analysis_thread,
                args=(found_files,),
                daemon=True
            ).start()
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"启动分析失败: {str(e)}"))

    def _batch_analysis_thread(self, found_files):
        """批量分析线程"""
        try:
            # 1. 分析CPU数据
            if os.path.exists(found_files['cpu']):
                self._analyze_cpu_thread(found_files['cpu'])

            # 2. 分析内存数据
            if os.path.exists(found_files['memory']):
                self._analyze_memory_thread(found_files['memory'])

            # 3. 分析性能数据
            if os.path.exists(found_files['performance']):
                self._analyze_performance_thread(found_files['performance'])

            # 提示完成
            self.root.after(0, lambda: messagebox.showinfo(
                "完成", "批量分析已完成！"
            ))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {str(e)}"))

    def on_close(self):
        """处理窗口关闭事件"""
        # 清除图表资源
        if self.cpu_figure:
            plt.close(self.cpu_figure)
        if self.memory_figure:
            plt.close(self.memory_figure)
        if self.performance_figure:
            plt.close(self.performance_figure)

        # 销毁窗口并强制退出
        self.root.destroy()
        import sys
        sys.exit()


def main():
    """主程序入口"""
    root = tk.Tk()
    app = CStandardAnalysisToolUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()

