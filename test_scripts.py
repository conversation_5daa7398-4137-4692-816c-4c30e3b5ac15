#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本启动
"""

import subprocess
import os
import sys

def test_script_launch(script_name):
    """测试脚本启动"""
    print(f"测试启动: {script_name}")
    
    if not os.path.exists(script_name):
        print(f"✗ 脚本文件不存在: {script_name}")
        return False
    
    try:
        print(f"正在启动 {script_name}...")
        
        # 启动脚本
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(['python', script_name], 
                                     creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            process = subprocess.Popen(['python', script_name])
        
        print(f"✓ {script_name} 启动成功，进程ID: {process.pid}")
        return True
        
    except Exception as e:
        print(f"✗ 启动失败: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("脚本启动测试")
    print("=" * 60)
    
    scripts = [
        'C标准分析工具UI.py',
        'Java标准化分析工具UI.py'
    ]
    
    for script in scripts:
        print(f"\n测试 {script}:")
        print("-" * 40)
        test_script_launch(script)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("如果脚本启动成功，应该会看到GUI窗口")
    print("=" * 60)

if __name__ == '__main__':
    main()
