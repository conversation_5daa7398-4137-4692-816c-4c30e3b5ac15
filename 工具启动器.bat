@echo off
chcp 65001 >nul
title 分析工具启动器

:menu
cls
echo ============================================================
echo                   分析工具启动器
echo ============================================================
echo.
echo 请选择要启动的工具:
echo.
echo 1. C标准化分析工具
echo 2. Java标准化分析工具  
echo 3. 同时启动两个工具
echo 4. 退出
echo.
echo ============================================================

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto start_c
if "%choice%"=="2" goto start_java
if "%choice%"=="3" goto start_both
if "%choice%"=="4" goto exit
goto invalid

:start_c
echo.
echo 🚀 启动 C标准化分析工具...
if exist "C标准分析工具UI.py" (
    start "C标准化分析工具" python "C标准分析工具UI.py"
    echo ✅ C标准化分析工具已启动！
) else (
    echo ❌ 错误: 找不到 C标准分析工具UI.py 文件
)
goto continue

:start_java
echo.
echo 🚀 启动 Java标准化分析工具...
if exist "Java标准化分析工具UI.py" (
    start "Java标准化分析工具" python "Java标准化分析工具UI.py"
    echo ✅ Java标准化分析工具已启动！
) else (
    echo ❌ 错误: 找不到 Java标准化分析工具UI.py 文件
)
goto continue

:start_both
echo.
echo 🚀 启动所有工具...
if exist "C标准分析工具UI.py" (
    start "C标准化分析工具" python "C标准分析工具UI.py"
    echo ✅ C标准化分析工具已启动！
) else (
    echo ❌ 错误: 找不到 C标准分析工具UI.py 文件
)

if exist "Java标准化分析工具UI.py" (
    start "Java标准化分析工具" python "Java标准化分析工具UI.py"
    echo ✅ Java标准化分析工具已启动！
) else (
    echo ❌ 错误: 找不到 Java标准化分析工具UI.py 文件
)
goto continue

:invalid
echo.
echo ❌ 无效选择，请重新输入
goto continue

:continue
echo.
pause
goto menu

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit
