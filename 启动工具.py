#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行工具启动器
"""

import subprocess
import os
import sys

def launch_tool(script_path, tool_name):
    """启动工具"""
    try:
        # 检查脚本文件是否存在
        if not os.path.exists(script_path):
            print(f"❌ 错误: 脚本文件不存在 - {script_path}")
            return False
        
        print(f"🚀 正在启动 {tool_name}...")
        
        # 启动脚本
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(['python', script_path], 
                                     creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            process = subprocess.Popen(['python', script_path])
        
        print(f"✅ {tool_name} 已启动！进程ID: {process.pid}")
        return True
        
    except Exception as e:
        print(f"❌ 启动 {tool_name} 失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("                   分析工具启动器")
    print("=" * 60)
    print()
    
    tools = {
        '1': ('C标准分析工具UI.py', 'C标准化分析工具'),
        '2': ('Java标准化分析工具UI.py', 'Java标准化分析工具')
    }
    
    while True:
        print("可用工具:")
        print("1. C标准化分析工具")
        print("2. Java标准化分析工具")
        print("3. 同时启动两个工具")
        print("4. 退出")
        print()
        
        choice = input("请选择要启动的工具 (1-4): ").strip()
        
        if choice == '1':
            script_path, tool_name = tools['1']
            launch_tool(script_path, tool_name)
            
        elif choice == '2':
            script_path, tool_name = tools['2']
            launch_tool(script_path, tool_name)
            
        elif choice == '3':
            print("🚀 启动所有工具...")
            for key in ['1', '2']:
                script_path, tool_name = tools[key]
                launch_tool(script_path, tool_name)
                
        elif choice == '4':
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")
        
        print()
        input("按回车键继续...")
        print()

if __name__ == "__main__":
    main()
