@echo off
chcp 65001 >nul
title 分析工具平台 - 一键部署

echo ============================================================
echo                   分析工具平台 - 一键部署
echo ============================================================
echo.

echo 第一步：检查Python脚本文件...
if not exist "C标准分析工具UI.py" (
    echo ❌ 错误: 找不到 C标准分析工具UI.py 文件
    goto :error
)
echo ✓ C标准分析工具UI.py 存在

if not exist "Java标准化分析工具UI.py" (
    echo ❌ 错误: 找不到 Java标准化分析工具UI.py 文件
    goto :error
)
echo ✓ Java标准化分析工具UI.py 存在

echo.
echo 第二步：安装打包工具...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    goto :error
)
echo ✓ PyInstaller安装成功

echo.
echo 第三步：打包Python脚本为EXE程序...
python 打包脚本.py
if errorlevel 1 (
    echo ❌ 打包失败
    goto :error
)

echo.
echo 第四步：检查生成的EXE程序...
if not exist "exe_programs\C标准分析工具.exe" (
    echo ❌ C标准分析工具.exe 生成失败
    goto :error
)
echo ✓ C标准分析工具.exe 生成成功

if not exist "exe_programs\Java标准化分析工具.exe" (
    echo ❌ Java标准化分析工具.exe 生成失败
    goto :error
)
echo ✓ Java标准化分析工具.exe 生成成功

echo.
echo 第五步：安装Web平台依赖...
cd web_analysis_platform
pip install flask
if errorlevel 1 (
    echo ❌ Flask安装失败
    goto :error
)
echo ✓ Flask安装成功

echo.
echo ============================================================
echo ✅ 部署完成！
echo ============================================================
echo.
echo 生成的EXE程序:
dir ..\exe_programs\*.exe /B
echo.
echo 检查商用车工具:
if exist "..\exe_programs\商用车标准化测试统计工具v2.4.exe" (
    echo ✓ 商用车标准化测试统计工具v2.4.exe 已存在
) else (
    echo ⚠ 商用车标准化测试统计工具v2.4.exe 不存在，请手动添加
)
echo.
echo 启动Web平台:
echo   方法1: 双击 web_analysis_platform\start.bat
echo   方法2: 运行 python web_analysis_platform\app.py
echo.
echo 访问地址: http://localhost:5000
echo 登录信息: lcj / 123
echo.
echo 功能说明:
echo - 点击工具按钮将直接启动对应的EXE程序
echo - EXE程序独立运行，无需Python环境
echo - 启动速度更快，用户体验更好
echo ============================================================
pause
exit /b 0

:error
echo.
echo ============================================================
echo ❌ 部署失败！
echo ============================================================
echo 请检查错误信息并重试
pause
exit /b 1
