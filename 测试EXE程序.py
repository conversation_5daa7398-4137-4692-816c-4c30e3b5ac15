#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EXE程序启动
"""

import os
import subprocess
import time

def test_exe_launch(exe_path, tool_name):
    """测试EXE程序启动"""
    print(f"测试启动: {tool_name}")
    print(f"EXE路径: {exe_path}")
    
    if not os.path.exists(exe_path):
        print(f"✗ EXE文件不存在: {exe_path}")
        return False
    
    # 获取文件大小
    size_mb = os.path.getsize(exe_path) / (1024 * 1024)
    print(f"文件大小: {size_mb:.1f} MB")
    
    try:
        print(f"正在启动 {tool_name}...")
        
        # 启动EXE程序
        process = subprocess.Popen([exe_path], cwd=os.path.dirname(exe_path))
        
        print(f"✓ {tool_name} 启动成功，进程ID: {process.pid}")
        print("注意：EXE程序应该已经在新窗口中打开")
        return True
        
    except Exception as e:
        print(f"✗ 启动失败: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("EXE程序启动测试")
    print("=" * 60)
    
    # 检查exe_programs目录
    if not os.path.exists('exe_programs'):
        print("✗ exe_programs目录不存在")
        print("请先运行打包脚本生成EXE程序")
        return
    
    # 要测试的EXE程序
    exe_programs = [
        ('exe_programs/C标准分析工具.exe', 'C标准分析工具'),
        ('exe_programs/Java标准化分析工具.exe', 'Java标准化分析工具')
    ]
    
    print("发现的EXE程序:")
    for exe_path, tool_name in exe_programs:
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"  ✓ {tool_name}: {exe_path} ({size_mb:.1f} MB)")
        else:
            print(f"  ✗ {tool_name}: {exe_path} (不存在)")
    
    print("\n选择要测试的程序:")
    print("1. C标准分析工具")
    print("2. Java标准化分析工具")
    print("3. 测试所有程序")
    print("4. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_exe_launch('exe_programs/C标准分析工具.exe', 'C标准分析工具')
            break
        elif choice == '2':
            test_exe_launch('exe_programs/Java标准化分析工具.exe', 'Java标准化分析工具')
            break
        elif choice == '3':
            print("\n测试所有EXE程序...")
            for exe_path, tool_name in exe_programs:
                print(f"\n{'-' * 40}")
                if test_exe_launch(exe_path, tool_name):
                    print("等待3秒后测试下一个...")
                    time.sleep(3)
            break
        elif choice == '4':
            print("退出测试")
            break
        else:
            print("无效选择，请重新输入")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("如果EXE程序启动成功，应该会看到对应的GUI窗口")
    print("=" * 60)

if __name__ == '__main__':
    main()
